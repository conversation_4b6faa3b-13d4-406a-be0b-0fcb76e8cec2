const mysql = require('mysql2/promise')
require('dotenv').config()

/**
 * 数据库连接池配置
 */
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root123',
  database: process.env.DB_NAME || 'onlineExamSystem',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4'
})

/**
 * 测试数据库连接
 */
async function testConnection() {
  try {
    const connection = await pool.getConnection()
    console.log('✅ MySQL数据库连接成功')
    connection.release()
    return true
  } catch (error) {
    console.error('❌ MySQL数据库连接失败:', error.message)
    return false
  }
}

/**
 * 执行SQL查询
 */
async function query(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params)
    return rows
  } catch (error) {
    console.error('SQL查询错误:', error)
    throw error
  }
}

/**
 * 开始事务
 */
async function beginTransaction() {
  const connection = await pool.getConnection()
  await connection.beginTransaction()
  return connection
}

/**
 * 提交事务
 */
async function commitTransaction(connection) {
  await connection.commit()
  connection.release()
}

/**
 * 回滚事务
 */
async function rollbackTransaction(connection) {
  await connection.rollback()
  connection.release()
}

module.exports = {
  pool,
  testConnection,
  query,
  beginTransaction,
  commitTransaction,
  rollbackTransaction
}
