const express = require('express');
const { prisma } = require('../config/prisma');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取题库列表（支持分页和筛选）
 */
router.get('/', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            course_id,
            question_type,
            difficulty,
            keyword
        } = req.query;

        const offset = (parseInt(page) - 1) * parseInt(limit);

        // 构建查询条件
        const where = {};
        if (course_id) where.course_id = parseInt(course_id);
        if (question_type) where.question_type = question_type;
        if (difficulty) where.difficulty = parseInt(difficulty);
        if (keyword) {
            where.question_text = {
                contains: keyword
            };
        }

        // 如果是教师，只能查看自己创建的题目
        if (req.user.role === 'teacher') {
            where.creator_id = req.user.user_id;
        }

        const [questions, total] = await Promise.all([
            prisma.question.findMany({
                where,
                include: {
                    course: {
                        select: {
                            course_name: true
                        }
                    },
                    creator: {
                        select: {
                            full_name: true
                        }
                    },
                    options: {
                        select: {
                            option_id: true,
                            option_text: true,
                            is_correct: true
                        }
                    }
                },
                orderBy: {
                    create_time: 'desc'
                },
                skip: offset,
                take: parseInt(limit)
            }),
            prisma.question.count({ where })
        ]);

        res.json({
            success: true,
            data: {
                questions,
                pagination: {
                    current_page: parseInt(page),
                    per_page: parseInt(limit),
                    total,
                    total_pages: Math.ceil(total / parseInt(limit))
                }
            }
        });

    } catch (error) {
        console.error('获取题库列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取单个题目详情
 */
router.get('/:id', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const questionId = parseInt(req.params.id);

        const question = await prisma.question.findUnique({
            where: {
                question_id: questionId
            },
            include: {
                course: {
                    select: {
                        course_name: true
                    }
                },
                creator: {
                    select: {
                        full_name: true
                    }
                },
                options: {
                    select: {
                        option_id: true,
                        option_text: true,
                        is_correct: true
                    }
                }
            }
        });

        if (!question) {
            return res.status(404).json({
                success: false,
                message: '题目不存在'
            });
        }

        // 如果是教师，只能查看自己创建的题目
        if (req.user.role === 'teacher' && question.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        res.json({
            success: true,
            data: question
        });

    } catch (error) {
        console.error('获取题目详情错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});/**
 * 
创建新题目
 */
router.post('/', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            course_id,
            question_text,
            question_type,
            difficulty = 2,
            options = []
        } = req.body;

        // 验证必填字段
        if (!course_id || !question_text || !question_type) {
            return res.status(400).json({
                success: false,
                message: '课程ID、题干内容和题型不能为空'
            });
        }

        // 验证题型
        const validTypes = ['single_choice', 'multiple_choice', 'true_false', 'fill_in_blank', 'essay'];
        if (!validTypes.includes(question_type)) {
            return res.status(400).json({
                success: false,
                message: '无效的题型'
            });
        }

        // 对于选择题和判断题，验证选项
        if (['single_choice', 'multiple_choice', 'true_false'].includes(question_type)) {
            if (!options || options.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '选择题和判断题必须提供选项'
                });
            }

            // 验证是否有正确答案
            const hasCorrectAnswer = options.some(option => option.is_correct);
            if (!hasCorrectAnswer) {
                return res.status(400).json({
                    success: false,
                    message: '必须设置至少一个正确答案'
                });
            }

            // 单选题只能有一个正确答案
            if (question_type === 'single_choice') {
                const correctCount = options.filter(option => option.is_correct).length;
                if (correctCount !== 1) {
                    return res.status(400).json({
                        success: false,
                        message: '单选题只能有一个正确答案'
                    });
                }
            }
        }

        // 使用事务创建题目和选项
        const result = await prisma.$transaction(async (tx) => {
            // 创建题目
            const question = await tx.question.create({
                data: {
                    course_id: parseInt(course_id),
                    question_text,
                    question_type,
                    difficulty: parseInt(difficulty),
                    creator_id: req.user.user_id
                }
            });

            // 如果有选项，创建选项
            if (options && options.length > 0) {
                await tx.questionOption.createMany({
                    data: options.map(option => ({
                        question_id: question.question_id,
                        option_text: option.option_text,
                        is_correct: Boolean(option.is_correct)
                    }))
                });
            }

            return question;
        });

        res.status(201).json({
            success: true,
            message: '题目创建成功',
            data: {
                question_id: result.question_id
            }
        });

    } catch (error) {
        console.error('创建题目错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 更新题目
 */
router.put('/:id', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const questionId = parseInt(req.params.id);
        const {
            course_id,
            question_text,
            question_type,
            difficulty,
            options = []
        } = req.body;

        // 检查题目是否存在
        const existingQuestion = await prisma.question.findUnique({
            where: {
                question_id: questionId
            }
        });

        if (!existingQuestion) {
            return res.status(404).json({
                success: false,
                message: '题目不存在'
            });
        }

        // 如果是教师，只能修改自己创建的题目
        if (req.user.role === 'teacher' && existingQuestion.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 验证题型
        if (question_type) {
            const validTypes = ['single_choice', 'multiple_choice', 'true_false', 'fill_in_blank', 'essay'];
            if (!validTypes.includes(question_type)) {
                return res.status(400).json({
                    success: false,
                    message: '无效的题型'
                });
            }
        }

        // 使用事务更新题目和选项
        await prisma.$transaction(async (tx) => {
            // 更新题目基本信息
            const updateData = {};
            if (course_id) updateData.course_id = parseInt(course_id);
            if (question_text) updateData.question_text = question_text;
            if (question_type) updateData.question_type = question_type;
            if (difficulty !== undefined) updateData.difficulty = parseInt(difficulty);

            await tx.question.update({
                where: {
                    question_id: questionId
                },
                data: updateData
            });

            // 如果提供了选项，先删除旧选项，再创建新选项
            if (options && options.length > 0) {
                await tx.questionOption.deleteMany({
                    where: {
                        question_id: questionId
                    }
                });

                await tx.questionOption.createMany({
                    data: options.map(option => ({
                        question_id: questionId,
                        option_text: option.option_text,
                        is_correct: Boolean(option.is_correct)
                    }))
                });
            }
        });

        res.json({
            success: true,
            message: '题目更新成功'
        });

    } catch (error) {
        console.error('更新题目错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 删除题目
 */
router.delete('/:id', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const questionId = parseInt(req.params.id);

        // 检查题目是否存在
        const existingQuestion = await prisma.question.findUnique({
            where: {
                question_id: questionId
            }
        });

        if (!existingQuestion) {
            return res.status(404).json({
                success: false,
                message: '题目不存在'
            });
        }

        // 如果是教师，只能删除自己创建的题目
        if (req.user.role === 'teacher' && existingQuestion.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 检查题目是否被试卷使用
        const examQuestionCount = await prisma.examQuestion.count({
            where: {
                question_id: questionId
            }
        });

        if (examQuestionCount > 0) {
            return res.status(400).json({
                success: false,
                message: '该题目已被试卷使用，无法删除'
            });
        }

        // 删除题目（选项会因为外键约束自动删除）
        await prisma.question.delete({
            where: {
                question_id: questionId
            }
        });

        res.json({
            success: true,
            message: '题目删除成功'
        });

    } catch (error) {
        console.error('删除题目错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 批量删除题目
 */
router.post('/batch-delete', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const { ids } = req.body;

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请提供要删除的题目ID列表'
            });
        }

        // 检查题目是否存在且有权限删除
        const questions = await prisma.question.findMany({
            where: {
                question_id: {
                    in: ids.map(id => parseInt(id))
                }
            }
        });

        if (questions.length !== ids.length) {
            return res.status(404).json({
                success: false,
                message: '部分题目不存在'
            });
        }

        // 如果是教师，只能删除自己创建的题目
        if (req.user.role === 'teacher') {
            const unauthorizedQuestions = questions.filter(q => q.creator_id !== req.user.user_id);
            if (unauthorizedQuestions.length > 0) {
                return res.status(403).json({
                    success: false,
                    message: '权限不足，无法删除部分题目'
                });
            }
        }

        // 检查题目是否被试卷使用
        const examQuestionCount = await prisma.examQuestion.count({
            where: {
                question_id: {
                    in: ids.map(id => parseInt(id))
                }
            }
        });

        if (examQuestionCount > 0) {
            return res.status(400).json({
                success: false,
                message: '部分题目已被试卷使用，无法删除'
            });
        }

        // 批量删除题目
        await prisma.question.deleteMany({
            where: {
                question_id: {
                    in: ids.map(id => parseInt(id))
                }
            }
        });

        res.json({
            success: true,
            message: `成功删除 ${ids.length} 个题目`
        });

    } catch (error) {
        console.error('批量删除题目错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;