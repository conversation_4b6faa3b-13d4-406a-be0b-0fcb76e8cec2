const express = require('express');
const { prisma } = require('../config/prisma');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取试卷列表（支持分页和筛选）
 */
router.get('/', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            course_id,
            keyword
        } = req.query;

        const offset = (parseInt(page) - 1) * parseInt(limit);

        // 构建查询条件
        const where = {};
        if (course_id) where.course_id = parseInt(course_id);
        if (keyword) {
            where.exam_name = {
                contains: keyword
            };
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher') {
            where.creator_id = req.user.user_id;
        }

        const [exams, total] = await Promise.all([
            prisma.exam.findMany({
                where,
                include: {
                    course: {
                        select: {
                            course_name: true
                        }
                    },
                    creator: {
                        select: {
                            full_name: true
                        }
                    },
                    _count: {
                        select: {
                            exam_questions: true
                        }
                    }
                },
                orderBy: {
                    create_time: 'desc'
                },
                skip: offset,
                take: parseInt(limit)
            }),
            prisma.exam.count({ where })
        ]);

        res.json({
            success: true,
            data: {
                exams,
                pagination: {
                    current_page: parseInt(page),
                    per_page: parseInt(limit),
                    total,
                    total_pages: Math.ceil(total / parseInt(limit))
                }
            }
        });

    } catch (error) {
        console.error('获取试卷列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取单个试卷详情
 */
router.get('/:id', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);

        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            },
            include: {
                course: {
                    select: {
                        course_name: true
                    }
                },
                creator: {
                    select: {
                        full_name: true
                    }
                },
                exam_questions: {
                    include: {
                        question: {
                            include: {
                                options: true
                            }
                        }
                    },
                    orderBy: {
                        exam_question_id: 'asc'
                    }
                }
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        res.json({
            success: true,
            data: exam
        });

    } catch (error) {
        console.error('获取试卷详情错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 创建试卷
 */
router.post('/', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            exam_name,
            course_id,
            start_time,
            end_time,
            duration,
            total_score = 100,
            questions = []
        } = req.body;

        // 验证必填字段
        if (!exam_name || !course_id || !start_time || !end_time || !duration) {
            return res.status(400).json({
                success: false,
                message: '试卷名称、课程、开始时间、结束时间和考试时长不能为空'
            });
        }

        // 验证时间逻辑
        const startTime = new Date(start_time);
        const endTime = new Date(end_time);
        
        if (startTime >= endTime) {
            return res.status(400).json({
                success: false,
                message: '开始时间必须早于结束时间'
            });
        }

        // 验证题目列表
        if (questions.length === 0) {
            return res.status(400).json({
                success: false,
                message: '试卷必须包含至少一道题目'
            });
        }

        // 验证题目是否存在
        const questionIds = questions.map(q => q.question_id);
        const existingQuestions = await prisma.question.findMany({
            where: {
                question_id: {
                    in: questionIds
                }
            }
        });

        if (existingQuestions.length !== questionIds.length) {
            return res.status(400).json({
                success: false,
                message: '部分题目不存在'
            });
        }

        // 使用事务创建试卷和关联题目
        const result = await prisma.$transaction(async (tx) => {
            // 创建试卷
            const exam = await tx.exam.create({
                data: {
                    exam_name,
                    course_id: parseInt(course_id),
                    creator_id: req.user.user_id,
                    start_time: startTime,
                    end_time: endTime,
                    duration: parseInt(duration),
                    total_score: parseInt(total_score)
                }
            });

            // 创建试卷题目关联
            if (questions.length > 0) {
                await tx.examQuestion.createMany({
                    data: questions.map(q => ({
                        exam_id: exam.exam_id,
                        question_id: q.question_id,
                        score: parseFloat(q.score || 0)
                    }))
                });
            }

            return exam;
        });

        res.status(201).json({
            success: true,
            message: '试卷创建成功',
            data: {
                exam_id: result.exam_id
            }
        });

    } catch (error) {
        console.error('创建试卷错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 更新试卷
 */
router.put('/:id', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);
        const {
            exam_name,
            course_id,
            start_time,
            end_time,
            duration,
            total_score,
            questions = []
        } = req.body;

        // 检查试卷是否存在
        const existingExam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!existingExam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能修改自己创建的试卷
        if (req.user.role === 'teacher' && existingExam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 验证时间逻辑
        if (start_time && end_time) {
            const startTime = new Date(start_time);
            const endTime = new Date(end_time);

            if (startTime >= endTime) {
                return res.status(400).json({
                    success: false,
                    message: '开始时间必须早于结束时间'
                });
            }
        }

        // 使用事务更新试卷
        await prisma.$transaction(async (tx) => {
            // 更新试卷基本信息
            const updateData = {};
            if (exam_name) updateData.exam_name = exam_name;
            if (course_id) updateData.course_id = parseInt(course_id);
            if (start_time) updateData.start_time = new Date(start_time);
            if (end_time) updateData.end_time = new Date(end_time);
            if (duration) updateData.duration = parseInt(duration);
            if (total_score !== undefined) updateData.total_score = parseInt(total_score);

            await tx.exam.update({
                where: {
                    exam_id: examId
                },
                data: updateData
            });

            // 如果提供了题目列表，更新试卷题目
            if (questions.length > 0) {
                // 先删除旧的题目关联
                await tx.examQuestion.deleteMany({
                    where: {
                        exam_id: examId
                    }
                });

                // 创建新的题目关联
                await tx.examQuestion.createMany({
                    data: questions.map(q => ({
                        exam_id: examId,
                        question_id: q.question_id,
                        score: parseFloat(q.score || 0)
                    }))
                });
            }
        });

        res.json({
            success: true,
            message: '试卷更新成功'
        });

    } catch (error) {
        console.error('更新试卷错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 删除试卷
 */
router.delete('/:id', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);

        // 检查试卷是否存在
        const existingExam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!existingExam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能删除自己创建的试卷
        if (req.user.role === 'teacher' && existingExam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 检查是否有学生已经参加考试
        const studentScoreCount = await prisma.studentScore.count({
            where: {
                exam_id: examId
            }
        });

        if (studentScoreCount > 0) {
            return res.status(400).json({
                success: false,
                message: '该试卷已有学生参加考试，无法删除'
            });
        }

        // 删除试卷（关联的题目会因为外键约束自动删除）
        await prisma.exam.delete({
            where: {
                exam_id: examId
            }
        });

        res.json({
            success: true,
            message: '试卷删除成功'
        });

    } catch (error) {
        console.error('删除试卷错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 自动组卷 - 根据条件随机选择题目
 */
router.post('/auto-generate', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            course_id,
            question_types = [],
            difficulties = [],
            question_count = 20,
            total_score = 100
        } = req.body;

        if (!course_id) {
            return res.status(400).json({
                success: false,
                message: '课程ID不能为空'
            });
        }

        // 构建查询条件
        const where = {
            course_id: parseInt(course_id)
        };

        // 如果是教师，只能选择自己创建的题目
        if (req.user.role === 'teacher') {
            where.creator_id = req.user.user_id;
        }

        if (question_types.length > 0) {
            where.question_type = {
                in: question_types
            };
        }

        if (difficulties.length > 0) {
            where.difficulty = {
                in: difficulties.map(d => parseInt(d))
            };
        }

        // 获取符合条件的题目
        const availableQuestions = await prisma.question.findMany({
            where,
            include: {
                options: true
            }
        });

        if (availableQuestions.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有符合条件的题目'
            });
        }

        if (availableQuestions.length < question_count) {
            return res.status(400).json({
                success: false,
                message: `符合条件的题目数量不足，仅有 ${availableQuestions.length} 道题目`
            });
        }

        // 随机选择题目
        const shuffled = availableQuestions.sort(() => 0.5 - Math.random());
        const selectedQuestions = shuffled.slice(0, question_count);

        // 计算每题分值
        const scorePerQuestion = total_score / question_count;

        // 格式化返回数据
        const questions = selectedQuestions.map(q => ({
            question_id: q.question_id,
            question_text: q.question_text,
            question_type: q.question_type,
            difficulty: q.difficulty,
            options: q.options,
            score: Math.round(scorePerQuestion * 10) / 10 // 保留一位小数
        }));

        res.json({
            success: true,
            data: {
                questions,
                total_questions: questions.length,
                total_score: total_score
            }
        });

    } catch (error) {
        console.error('自动组卷错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取可用于组卷的题目列表
 */
router.get('/questions/available', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            course_id,
            question_type,
            difficulty,
            keyword,
            page = 1,
            limit = 50
        } = req.query;

        if (!course_id) {
            return res.status(400).json({
                success: false,
                message: '课程ID不能为空'
            });
        }

        const offset = (parseInt(page) - 1) * parseInt(limit);

        // 构建查询条件
        const where = {
            course_id: parseInt(course_id)
        };

        // 如果是教师，只能选择自己创建的题目
        if (req.user.role === 'teacher') {
            where.creator_id = req.user.user_id;
        }

        if (question_type) where.question_type = question_type;
        if (difficulty) where.difficulty = parseInt(difficulty);
        if (keyword) {
            where.question_text = {
                contains: keyword
            };
        }

        const [questions, total] = await Promise.all([
            prisma.question.findMany({
                where,
                include: {
                    options: {
                        select: {
                            option_id: true,
                            option_text: true,
                            is_correct: true
                        }
                    }
                },
                orderBy: {
                    create_time: 'desc'
                },
                skip: offset,
                take: parseInt(limit)
            }),
            prisma.question.count({ where })
        ]);

        res.json({
            success: true,
            data: {
                questions,
                pagination: {
                    current_page: parseInt(page),
                    per_page: parseInt(limit),
                    total,
                    total_pages: Math.ceil(total / parseInt(limit))
                }
            }
        });

    } catch (error) {
        console.error('获取可用题目列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 发布考试 - 为指定学生创建考试记录
 */
router.post('/:id/publish', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);
        const { student_ids = [] } = req.body;

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能发布自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 验证学生ID
        if (student_ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请选择参加考试的学生'
            });
        }

        // 检查学生是否存在
        const students = await prisma.user.findMany({
            where: {
                user_id: {
                    in: student_ids.map(id => parseInt(id))
                },
                role: 'student'
            }
        });

        if (students.length !== student_ids.length) {
            return res.status(400).json({
                success: false,
                message: '部分学生不存在或不是学生角色'
            });
        }

        // 检查是否已经发布给这些学生
        const existingRecords = await prisma.studentScore.findMany({
            where: {
                exam_id: examId,
                student_id: {
                    in: student_ids.map(id => parseInt(id))
                }
            }
        });

        if (existingRecords.length > 0) {
            return res.status(400).json({
                success: false,
                message: '部分学生已经参加过此考试'
            });
        }

        // 为每个学生创建考试记录
        await prisma.studentScore.createMany({
            data: student_ids.map(studentId => ({
                exam_id: examId,
                student_id: parseInt(studentId),
                status: 'pending'
            }))
        });

        res.json({
            success: true,
            message: `成功发布考试给 ${student_ids.length} 名学生`
        });

    } catch (error) {
        console.error('发布考试错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取考试参与学生列表
 */
router.get('/:id/students', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 获取参与考试的学生列表
        const studentScores = await prisma.studentScore.findMany({
            where: {
                exam_id: examId
            },
            include: {
                student: {
                    select: {
                        user_id: true,
                        username: true,
                        full_name: true,
                        email: true
                    }
                }
            },
            orderBy: {
                student_id: 'asc'
            }
        });

        res.json({
            success: true,
            data: studentScores
        });

    } catch (error) {
        console.error('获取考试学生列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 取消发布考试 - 删除学生考试记录
 */
router.delete('/:id/unpublish', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);
        const { student_ids = [] } = req.body;

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能操作自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 构建删除条件
        const whereCondition = {
            exam_id: examId
        };

        if (student_ids.length > 0) {
            whereCondition.student_id = {
                in: student_ids.map(id => parseInt(id))
            };
        }

        // 检查是否有学生已经开始考试
        const submittedRecords = await prisma.studentScore.findMany({
            where: {
                ...whereCondition,
                status: {
                    in: ['submitted', 'marked']
                }
            }
        });

        if (submittedRecords.length > 0) {
            return res.status(400).json({
                success: false,
                message: '部分学生已经开始考试或已提交，无法取消发布'
            });
        }

        // 删除考试记录
        const deleteResult = await prisma.studentScore.deleteMany({
            where: whereCondition
        });

        res.json({
            success: true,
            message: `成功取消发布，影响 ${deleteResult.count} 条记录`
        });

    } catch (error) {
        console.error('取消发布考试错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取可选择的学生列表（用于发布考试）
 */
router.get('/students/available', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const {
            keyword,
            page = 1,
            limit = 50
        } = req.query;

        const offset = (parseInt(page) - 1) * parseInt(limit);

        // 构建查询条件
        const where = {
            role: 'student'
        };

        if (keyword) {
            where.OR = [
                {
                    username: {
                        contains: keyword
                    }
                },
                {
                    full_name: {
                        contains: keyword
                    }
                }
            ];
        }

        const [students, total] = await Promise.all([
            prisma.user.findMany({
                where,
                select: {
                    user_id: true,
                    username: true,
                    full_name: true,
                    email: true
                },
                orderBy: {
                    username: 'asc'
                },
                skip: offset,
                take: parseInt(limit)
            }),
            prisma.user.count({ where })
        ]);

        res.json({
            success: true,
            data: {
                students,
                pagination: {
                    current_page: parseInt(page),
                    per_page: parseInt(limit),
                    total,
                    total_pages: Math.ceil(total / parseInt(limit))
                }
            }
        });

    } catch (error) {
        console.error('获取学生列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取考试状态统计
 */
router.get('/:id/statistics', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.id);

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 获取考试统计数据
        const [
            totalStudents,
            pendingCount,
            submittedCount,
            markedCount
        ] = await Promise.all([
            prisma.studentScore.count({
                where: { exam_id: examId }
            }),
            prisma.studentScore.count({
                where: { exam_id: examId, status: 'pending' }
            }),
            prisma.studentScore.count({
                where: { exam_id: examId, status: 'submitted' }
            }),
            prisma.studentScore.count({
                where: { exam_id: examId, status: 'marked' }
            })
        ]);

        // 计算平均分（仅已批阅的）
        const markedScores = await prisma.studentScore.findMany({
            where: {
                exam_id: examId,
                status: 'marked',
                total_score: {
                    not: null
                }
            },
            select: {
                total_score: true
            }
        });

        const averageScore = markedScores.length > 0
            ? markedScores.reduce((sum, record) => sum + (record.total_score || 0), 0) / markedScores.length
            : 0;

        res.json({
            success: true,
            data: {
                total_students: totalStudents,
                pending_count: pendingCount,
                submitted_count: submittedCount,
                marked_count: markedCount,
                average_score: Math.round(averageScore * 100) / 100,
                completion_rate: totalStudents > 0 ? Math.round((submittedCount + markedCount) / totalStudents * 100) : 0
            }
        });

    } catch (error) {
        console.error('获取考试统计错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
