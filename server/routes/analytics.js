const express = require('express');
const { prisma } = require('../config/prisma');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取考试成绩统计分析
 */
router.get('/exam/:examId/statistics', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.examId);

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            },
            include: {
                course: {
                    select: {
                        course_name: true
                    }
                }
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 获取所有已批阅的成绩
        const markedScores = await prisma.studentScore.findMany({
            where: {
                exam_id: examId,
                status: 'marked',
                total_score: {
                    not: null
                }
            },
            include: {
                student: {
                    select: {
                        username: true,
                        full_name: true
                    }
                }
            },
            orderBy: {
                total_score: 'desc'
            }
        });

        if (markedScores.length === 0) {
            return res.json({
                success: true,
                data: {
                    exam_info: {
                        exam_name: exam.exam_name,
                        course_name: exam.course.course_name,
                        total_score: exam.total_score
                    },
                    basic_stats: {
                        total_students: 0,
                        average_score: 0,
                        highest_score: 0,
                        lowest_score: 0,
                        pass_rate: 0,
                        excellent_rate: 0
                    },
                    score_distribution: [],
                    student_rankings: []
                }
            });
        }

        const scores = markedScores.map(s => s.total_score);
        const totalStudents = scores.length;
        const totalScore = exam.total_score;

        // 基础统计
        const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalStudents;
        const highestScore = Math.max(...scores);
        const lowestScore = Math.min(...scores);
        
        // 及格率（60%以上）
        const passCount = scores.filter(score => score >= totalScore * 0.6).length;
        const passRate = (passCount / totalStudents) * 100;
        
        // 优秀率（85%以上）
        const excellentCount = scores.filter(score => score >= totalScore * 0.85).length;
        const excellentRate = (excellentCount / totalStudents) * 100;

        // 分数段分布
        const scoreRanges = [
            { range: '90-100', min: totalScore * 0.9, max: totalScore },
            { range: '80-89', min: totalScore * 0.8, max: totalScore * 0.9 - 0.1 },
            { range: '70-79', min: totalScore * 0.7, max: totalScore * 0.8 - 0.1 },
            { range: '60-69', min: totalScore * 0.6, max: totalScore * 0.7 - 0.1 },
            { range: '0-59', min: 0, max: totalScore * 0.6 - 0.1 }
        ];

        const scoreDistribution = scoreRanges.map(range => {
            const count = scores.filter(score => score >= range.min && score <= range.max).length;
            return {
                range: range.range,
                count,
                percentage: Math.round((count / totalStudents) * 100)
            };
        });

        // 学生排名（前20名）
        const studentRankings = markedScores.slice(0, 20).map((score, index) => ({
            rank: index + 1,
            student_name: score.student.full_name,
            username: score.student.username,
            score: score.total_score,
            percentage: Math.round((score.total_score / totalScore) * 100)
        }));

        res.json({
            success: true,
            data: {
                exam_info: {
                    exam_name: exam.exam_name,
                    course_name: exam.course.course_name,
                    total_score: totalScore
                },
                basic_stats: {
                    total_students: totalStudents,
                    average_score: Math.round(averageScore * 100) / 100,
                    highest_score: highestScore,
                    lowest_score: lowestScore,
                    pass_rate: Math.round(passRate * 100) / 100,
                    excellent_rate: Math.round(excellentRate * 100) / 100
                },
                score_distribution: scoreDistribution,
                student_rankings: studentRankings
            }
        });

    } catch (error) {
        console.error('获取考试统计错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取题目正确率分析
 */
router.get('/exam/:examId/question-analysis', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const examId = parseInt(req.params.examId);

        // 检查试卷是否存在
        const exam = await prisma.exam.findUnique({
            where: {
                exam_id: examId
            }
        });

        if (!exam) {
            return res.status(404).json({
                success: false,
                message: '试卷不存在'
            });
        }

        // 如果是教师，只能查看自己创建的试卷
        if (req.user.role === 'teacher' && exam.creator_id !== req.user.user_id) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 获取试卷题目和学生答案
        const examQuestions = await prisma.examQuestion.findMany({
            where: {
                exam_id: examId
            },
            include: {
                question: {
                    include: {
                        options: true
                    }
                }
            },
            orderBy: {
                exam_question_id: 'asc'
            }
        });

        const questionAnalysis = [];

        for (const examQuestion of examQuestions) {
            const question = examQuestion.question;
            
            // 获取该题的所有学生答案
            const studentAnswers = await prisma.studentAnswer.findMany({
                where: {
                    question_id: question.question_id,
                    studentScore: {
                        exam_id: examId,
                        status: {
                            in: ['submitted', 'marked']
                        }
                    }
                }
            });

            const totalAnswers = studentAnswers.length;
            if (totalAnswers === 0) continue;

            // 计算正确率
            const correctAnswers = studentAnswers.filter(answer => {
                if (question.question_type === 'single_choice' || question.question_type === 'true_false') {
                    return answer.student_answer === question.correct_answer;
                } else if (question.question_type === 'multiple_choice') {
                    const studentAnswers = answer.student_answer ? answer.student_answer.split(',').sort() : [];
                    const correctAnswers = question.correct_answer ? question.correct_answer.split(',').sort() : [];
                    return studentAnswers.length === correctAnswers.length &&
                        studentAnswers.every(ans => correctAnswers.includes(ans));
                } else {
                    // 主观题根据得分判断
                    return answer.score > 0;
                }
            });

            const correctRate = (correctAnswers.length / totalAnswers) * 100;

            // 选项分析（仅选择题）
            let optionAnalysis = null;
            if (['single_choice', 'multiple_choice'].includes(question.question_type)) {
                const optionStats = {};
                
                studentAnswers.forEach(answer => {
                    if (answer.student_answer) {
                        const selectedOptions = answer.student_answer.split(',');
                        selectedOptions.forEach(option => {
                            optionStats[option] = (optionStats[option] || 0) + 1;
                        });
                    }
                });

                optionAnalysis = question.options.map(option => ({
                    option_text: option.option_text,
                    selected_count: optionStats[option.option_text] || 0,
                    selected_rate: Math.round(((optionStats[option.option_text] || 0) / totalAnswers) * 100),
                    is_correct: question.correct_answer?.includes(option.option_text) || false
                }));
            }

            questionAnalysis.push({
                question_id: question.question_id,
                question_text: question.question_text,
                question_type: question.question_type,
                total_score: examQuestion.score,
                total_answers: totalAnswers,
                correct_count: correctAnswers.length,
                correct_rate: Math.round(correctRate * 100) / 100,
                average_score: studentAnswers.reduce((sum, answer) => sum + (answer.score || 0), 0) / totalAnswers,
                option_analysis: optionAnalysis
            });
        }

        res.json({
            success: true,
            data: questionAnalysis
        });

    } catch (error) {
        console.error('获取题目分析错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取教师的整体教学统计
 */
router.get('/teacher/overview', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const teacherId = req.user.role === 'teacher' ? req.user.user_id : null;

        if (!teacherId && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }

        // 构建查询条件
        const whereCondition = teacherId ? { creator_id: teacherId } : {};

        const [
            totalExams,
            totalQuestions,
            totalStudentScores,
            recentExams
        ] = await Promise.all([
            // 总考试数
            prisma.exam.count({
                where: whereCondition
            }),
            // 总题目数
            prisma.question.count({
                where: teacherId ? { creator_id: teacherId } : {}
            }),
            // 总参与考试人次
            prisma.studentScore.count({
                where: {
                    exam: whereCondition
                }
            }),
            // 最近的考试
            prisma.exam.findMany({
                where: whereCondition,
                include: {
                    course: {
                        select: {
                            course_name: true
                        }
                    },
                    _count: {
                        select: {
                            studentScores: true
                        }
                    }
                },
                orderBy: {
                    create_time: 'desc'
                },
                take: 5
            })
        ]);

        // 计算平均分
        const markedScores = await prisma.studentScore.findMany({
            where: {
                exam: whereCondition,
                status: 'marked',
                total_score: {
                    not: null
                }
            },
            select: {
                total_score: true,
                exam: {
                    select: {
                        total_score: true
                    }
                }
            }
        });

        const averageScore = markedScores.length > 0
            ? markedScores.reduce((sum, score) => {
                const percentage = (score.total_score / score.exam.total_score) * 100;
                return sum + percentage;
            }, 0) / markedScores.length
            : 0;

        res.json({
            success: true,
            data: {
                overview: {
                    total_exams: totalExams,
                    total_questions: totalQuestions,
                    total_participants: totalStudentScores,
                    average_score_percentage: Math.round(averageScore * 100) / 100
                },
                recent_exams: recentExams.map(exam => ({
                    exam_id: exam.exam_id,
                    exam_name: exam.exam_name,
                    course_name: exam.course.course_name,
                    start_time: exam.start_time,
                    participant_count: exam._count.studentScores,
                    create_time: exam.create_time
                }))
            }
        });

    } catch (error) {
        console.error('获取教师统计错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取课程成绩对比分析
 */
router.get('/course/:courseId/comparison', authenticateToken, authorizeRoles('teacher', 'admin'), async (req, res) => {
    try {
        const courseId = parseInt(req.params.courseId);

        // 获取该课程的所有考试
        const exams = await prisma.exam.findMany({
            where: {
                course_id: courseId,
                ...(req.user.role === 'teacher' ? { creator_id: req.user.user_id } : {})
            },
            include: {
                studentScores: {
                    where: {
                        status: 'marked',
                        total_score: {
                            not: null
                        }
                    }
                }
            },
            orderBy: {
                start_time: 'asc'
            }
        });

        const comparisonData = exams.map(exam => {
            const scores = exam.studentScores.map(s => s.total_score);
            const totalStudents = scores.length;

            if (totalStudents === 0) {
                return {
                    exam_id: exam.exam_id,
                    exam_name: exam.exam_name,
                    start_time: exam.start_time,
                    total_students: 0,
                    average_score: 0,
                    pass_rate: 0,
                    highest_score: 0,
                    lowest_score: 0
                };
            }

            const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalStudents;
            const passCount = scores.filter(score => score >= exam.total_score * 0.6).length;
            const passRate = (passCount / totalStudents) * 100;

            return {
                exam_id: exam.exam_id,
                exam_name: exam.exam_name,
                start_time: exam.start_time,
                total_students: totalStudents,
                average_score: Math.round(averageScore * 100) / 100,
                pass_rate: Math.round(passRate * 100) / 100,
                highest_score: Math.max(...scores),
                lowest_score: Math.min(...scores)
            };
        });

        res.json({
            success: true,
            data: comparisonData
        });

    } catch (error) {
        console.error('获取课程对比分析错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
