
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  user_id: 'user_id',
  username: 'username',
  password: 'password',
  full_name: 'full_name',
  role: 'role',
  email: 'email',
  student_id: 'student_id',
  create_time: 'create_time'
};

exports.Prisma.CourseScalarFieldEnum = {
  course_id: 'course_id',
  course_name: 'course_name',
  description: 'description'
};

exports.Prisma.QuestionScalarFieldEnum = {
  question_id: 'question_id',
  course_id: 'course_id',
  question_text: 'question_text',
  question_type: 'question_type',
  difficulty: 'difficulty',
  creator_id: 'creator_id',
  create_time: 'create_time'
};

exports.Prisma.QuestionOptionScalarFieldEnum = {
  option_id: 'option_id',
  question_id: 'question_id',
  option_text: 'option_text',
  is_correct: 'is_correct'
};

exports.Prisma.ExamScalarFieldEnum = {
  exam_id: 'exam_id',
  exam_name: 'exam_name',
  course_id: 'course_id',
  creator_id: 'creator_id',
  start_time: 'start_time',
  end_time: 'end_time',
  duration: 'duration',
  total_score: 'total_score',
  create_time: 'create_time'
};

exports.Prisma.ExamQuestionScalarFieldEnum = {
  exam_question_id: 'exam_question_id',
  exam_id: 'exam_id',
  question_id: 'question_id',
  score: 'score'
};

exports.Prisma.StudentScoreScalarFieldEnum = {
  score_id: 'score_id',
  exam_id: 'exam_id',
  student_id: 'student_id',
  total_score: 'total_score',
  submission_time: 'submission_time',
  status: 'status'
};

exports.Prisma.StudentAnswerScalarFieldEnum = {
  answer_id: 'answer_id',
  score_id: 'score_id',
  question_id: 'question_id',
  student_answer: 'student_answer',
  score: 'score',
  is_marked: 'is_marked'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  username: 'username',
  password: 'password',
  full_name: 'full_name',
  email: 'email',
  student_id: 'student_id'
};

exports.Prisma.CourseOrderByRelevanceFieldEnum = {
  course_name: 'course_name',
  description: 'description'
};

exports.Prisma.QuestionOrderByRelevanceFieldEnum = {
  question_text: 'question_text'
};

exports.Prisma.QuestionOptionOrderByRelevanceFieldEnum = {
  option_text: 'option_text'
};

exports.Prisma.ExamOrderByRelevanceFieldEnum = {
  exam_name: 'exam_name'
};

exports.Prisma.StudentAnswerOrderByRelevanceFieldEnum = {
  student_answer: 'student_answer'
};
exports.Role = exports.$Enums.Role = {
  student: 'student',
  teacher: 'teacher',
  admin: 'admin'
};

exports.QuestionType = exports.$Enums.QuestionType = {
  single_choice: 'single_choice',
  multiple_choice: 'multiple_choice',
  true_false: 'true_false',
  fill_in_blank: 'fill_in_blank',
  essay: 'essay'
};

exports.StudentScoreStatus = exports.$Enums.StudentScoreStatus = {
  pending: 'pending',
  submitted: 'submitted',
  marked: 'marked'
};

exports.Prisma.ModelName = {
  User: 'User',
  Course: 'Course',
  Question: 'Question',
  QuestionOption: 'QuestionOption',
  Exam: 'Exam',
  ExamQuestion: 'ExamQuestion',
  StudentScore: 'StudentScore',
  StudentAnswer: 'StudentAnswer'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
