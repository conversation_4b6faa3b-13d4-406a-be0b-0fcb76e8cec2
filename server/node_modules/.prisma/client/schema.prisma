// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  user_id     Int      @id @default(autoincrement())
  username    String   @unique @db.VarChar(50)
  password    String   @db.VarChar(255)
  full_name   String?  @db.VarChar(50)
  role        Role
  email       String?  @db.VarChar(100)
  student_id  String?  @db.VarChar(50)
  create_time DateTime @default(now()) @db.Timestamp(0)

  // 关系
  created_questions Question[]
  created_exams     Exam[]
  student_scores    StudentScore[]

  @@map("users")
}

model Course {
  course_id   Int     @id @default(autoincrement())
  course_name String  @db.VarChar(100)
  description String? @db.Text

  // 关系
  questions Question[]
  exams     Exam[]

  @@map("courses")
}

model Question {
  question_id   Int          @id @default(autoincrement())
  course_id     Int
  question_text String       @db.Text
  question_type QuestionType
  difficulty    Int?         @default(2) @db.TinyInt
  creator_id    Int
  create_time   DateTime     @default(now()) @db.Timestamp(0)

  // 关系
  course          Course           @relation(fields: [course_id], references: [course_id], onDelete: Cascade)
  creator         User             @relation(fields: [creator_id], references: [user_id], onDelete: Cascade)
  options         QuestionOption[]
  exam_questions  ExamQuestion[]
  student_answers StudentAnswer[]

  @@index([course_id])
  @@map("questions")
}

model QuestionOption {
  option_id   Int     @id @default(autoincrement())
  question_id Int
  option_text String  @db.Text
  is_correct  Boolean @default(false)

  // 关系
  question Question @relation(fields: [question_id], references: [question_id], onDelete: Cascade)

  @@index([question_id])
  @@map("question_options")
}

model Exam {
  exam_id     Int      @id @default(autoincrement())
  exam_name   String   @db.VarChar(255)
  course_id   Int
  creator_id  Int
  start_time  DateTime @db.DateTime
  end_time    DateTime @db.DateTime
  duration    Int
  total_score Int?     @default(100)
  create_time DateTime @default(now()) @db.Timestamp(0)

  // 关系
  course         Course         @relation(fields: [course_id], references: [course_id])
  creator        User           @relation(fields: [creator_id], references: [user_id])
  exam_questions ExamQuestion[]
  student_scores StudentScore[]

  @@map("exams")
}

model ExamQuestion {
  exam_question_id Int     @id @default(autoincrement())
  exam_id          Int
  question_id      Int
  score            Decimal @db.Decimal(5, 1)

  // 关系
  exam     Exam     @relation(fields: [exam_id], references: [exam_id], onDelete: Cascade)
  question Question @relation(fields: [question_id], references: [question_id], onDelete: Cascade)

  @@unique([exam_id, question_id])
  @@map("exam_questions")
}

model StudentScore {
  score_id        Int                @id @default(autoincrement())
  exam_id         Int
  student_id      Int
  total_score     Decimal?           @db.Decimal(5, 1)
  submission_time DateTime?          @db.DateTime
  status          StudentScoreStatus @default(pending)

  // 关系
  exam            Exam            @relation(fields: [exam_id], references: [exam_id], onDelete: Cascade)
  student         User            @relation(fields: [student_id], references: [user_id], onDelete: Cascade)
  student_answers StudentAnswer[]

  @@unique([exam_id, student_id])
  @@map("student_scores")
}

model StudentAnswer {
  answer_id      Int     @id @default(autoincrement())
  score_id       Int
  question_id    Int
  student_answer String? @db.Text
  score          Decimal @default(0.0) @db.Decimal(5, 1)
  is_marked      Boolean @default(false)

  // 关系
  student_score StudentScore @relation(fields: [score_id], references: [score_id], onDelete: Cascade)
  question      Question     @relation(fields: [question_id], references: [question_id], onDelete: Cascade)

  @@map("student_answers")
}

enum Role {
  student
  teacher
  admin
}

enum QuestionType {
  single_choice
  multiple_choice
  true_false
  fill_in_blank
  essay
}

enum StudentScoreStatus {
  pending
  submitted
  marked
}
