const express = require('express');
const cors = require('cors');
const { testConnection } = require('./config/db');
const { testPrismaConnection } = require('./config/prisma');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 导入路由
const authRoutes = require('./routes/auth');
const courseRoutes = require('./routes/courses');
const questionRoutes = require('./routes/questions');
const examRoutes = require('./routes/exams');
const gradingRoutes = require('./routes/grading');
const analyticsRoutes = require('./routes/analytics');

// 基础路由
app.get('/', (req, res) => {
    res.json({
        message: '在线考试系统API服务',
        version: '1.0.0',
        status: 'running'
    });
});

// 健康检查路由
app.get('/health', async (req, res) => {
    const dbStatus = await testConnection();
    const prismaStatus = await testPrismaConnection();

    res.json({
        status: 'ok',
        database: dbStatus ? 'connected' : 'disconnected',
        prisma: prismaStatus ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString()
    });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/courses', courseRoutes);
app.use('/api/questions', questionRoutes);
app.use('/api/exams', examRoutes);
app.use('/api/grading', gradingRoutes);
app.use('/api/analytics', analyticsRoutes);

// 启动服务器
app.listen(PORT, async () => {
    console.log(`🚀 服务器运行在 http://localhost:${PORT}`);

    // 测试数据库连接
    await testConnection();
    await testPrismaConnection();
});

module.exports = app;