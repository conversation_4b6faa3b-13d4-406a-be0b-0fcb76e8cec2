<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="选择题目"
    width="1200px"
    @close="handleClose"
  >
    <!-- 搜索和筛选 -->
    <div class="filters">
      <el-form :model="queryParams" inline>
        <el-form-item label="题型">
          <el-select v-model="queryParams.question_type" placeholder="选择题型" clearable style="width: 150px">
            <el-option label="单选题" value="single_choice" />
            <el-option label="多选题" value="multiple_choice" />
            <el-option label="判断题" value="true_false" />
            <el-option label="填空题" value="fill_in_blank" />
            <el-option label="简答题" value="essay" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select v-model="queryParams.difficulty" placeholder="选择难度" clearable style="width: 120px">
            <el-option label="简单" :value="1" />
            <el-option label="中等" :value="2" />
            <el-option label="困难" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索题目内容"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadQuestions">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 题目列表 -->
    <div class="question-list">
      <div class="list-header">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选 (已选 {{ selectedQuestionIds.length }} / {{ questions.length }})
        </el-checkbox>
      </div>

      <div v-loading="loading" class="questions">
        <!-- 没有选择课程的提示 -->
        <div v-if="!courseId" class="no-course-tip">
          <el-empty description="请先在试卷信息中选择课程，然后再选择题目" />
        </div>

        <!-- 没有题目的提示 -->
        <div v-else-if="questions.length === 0 && !loading" class="no-questions-tip">
          <el-empty description="该课程下暂无可用题目，请先在题库管理中添加题目" />
        </div>

        <!-- 题目列表 -->
        <div
          v-for="question in questions"
          :key="question.question_id"
          class="question-item"
          :class="{ selected: selectedQuestionIds.includes(question.question_id) }"
        >
          <div class="question-header">
            <el-checkbox
              :model-value="selectedQuestionIds.includes(question.question_id)"
              @change="(checked: boolean) => handleQuestionSelect(question, checked)"
            />
            <el-tag :type="getQuestionTypeColor(question.question_type)" size="small">
              {{ getQuestionTypeName(question.question_type) }}
            </el-tag>
            <el-tag :type="getDifficultyColor(question.difficulty)" size="small">
              {{ getDifficultyName(question.difficulty) }}
            </el-tag>
          </div>

          <div class="question-content">
            <div class="question-text">{{ question.question_text }}</div>

            <!-- 选择题选项 -->
            <div v-if="['single_choice', 'multiple_choice'].includes(question.question_type)" class="options">
              <div
                v-for="option in question.options"
                :key="option.option_id"
                class="option"
                :class="{ correct: option.is_correct }"
              >
                {{ option.option_text }}
                <el-icon v-if="option.is_correct" class="correct-icon">
                  <Check />
                </el-icon>
              </div>
            </div>

            <!-- 判断题答案 -->
            <div v-else-if="question.question_type === 'true_false'" class="answer">
              <span>正确答案：</span>
              <el-tag :type="question.correct_answer === 'true' ? 'success' : 'danger'" size="small">
                {{ question.correct_answer === 'true' ? '正确' : '错误' }}
              </el-tag>
            </div>

            <!-- 填空题和简答题答案 -->
            <div v-else-if="['fill_in_blank', 'essay'].includes(question.question_type)" class="answer">
              <span>参考答案：</span>
              <span class="answer-text">{{ question.correct_answer }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadQuestions"
          @current-change="loadQuestions"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">
          确定选择 ({{ selectedQuestionIds.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { examApi } from '@/api'
import type { Question, QuestionQueryParams } from '@/types/api'

// Props
interface Props {
  visible: boolean
  courseId?: number
  selectedQuestions?: Question[]
}

const props = withDefaults(defineProps<Props>(), {
  courseId: 0,
  selectedQuestions: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [questions: Question[]]
}>()

// 响应式数据
const loading = ref(false)
const questions = ref<Question[]>([])
const total = ref(0)
const selectedQuestionIds = ref<number[]>([])

// 查询参数
const queryParams = reactive<QuestionQueryParams & { course_id: number }>({
  page: 1,
  limit: 20,
  course_id: props.courseId,
  question_type: '',
  difficulty: undefined,
  keyword: ''
})

// 计算属性
const selectAll = computed({
  get: () => selectedQuestionIds.value.length === questions.value.length && questions.value.length > 0,
  set: () => {}
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedQuestionIds.value.length
  return selectedCount > 0 && selectedCount < questions.value.length
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    queryParams.course_id = props.courseId
    selectedQuestionIds.value = props.selectedQuestions?.map(q => q.question_id) || []
    loadQuestions()
  }
})

// 加载题目列表
const loadQuestions = async () => {
  if (!props.courseId) {
    console.log('courseId为空，无法加载题目')
    questions.value = []
    total.value = 0
    return
  }

  try {
    loading.value = true
    const response = await examApi.getAvailableQuestions(queryParams)
    questions.value = response.data.questions
    total.value = response.data.pagination.total
  } catch (error) {
    console.error('加载题目列表失败:', error)
    ElMessage.error('加载题目列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  queryParams.question_type = ''
  queryParams.difficulty = undefined
  queryParams.keyword = ''
  queryParams.page = 1
  loadQuestions()
}

// 全选/取消全选
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedQuestionIds.value = [...new Set([...selectedQuestionIds.value, ...questions.value.map(q => q.question_id)])]
  } else {
    const currentPageIds = questions.value.map(q => q.question_id)
    selectedQuestionIds.value = selectedQuestionIds.value.filter(id => !currentPageIds.includes(id))
  }
}

// 单个题目选择
const handleQuestionSelect = (question: Question, checked: boolean) => {
  if (checked) {
    if (!selectedQuestionIds.value.includes(question.question_id)) {
      selectedQuestionIds.value.push(question.question_id)
    }
  } else {
    const index = selectedQuestionIds.value.indexOf(question.question_id)
    if (index > -1) {
      selectedQuestionIds.value.splice(index, 1)
    }
  }
}

// 工具函数
const getQuestionTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getQuestionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    single_choice: 'primary',
    multiple_choice: 'success',
    true_false: 'warning',
    fill_in_blank: 'info',
    essay: 'danger'
  }
  return colorMap[type] || ''
}

const getDifficultyName = (difficulty: number) => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

const getDifficultyColor = (difficulty: number) => {
  const colorMap: Record<number, string> = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return colorMap[difficulty] || ''
}

// 确认选择
const handleConfirm = () => {
  // 获取所有已选择的题目（包括其他页面的）
  const allSelectedQuestions: Question[] = []

  // 添加当前页面的选中题目
  questions.value.forEach(question => {
    if (selectedQuestionIds.value.includes(question.question_id)) {
      allSelectedQuestions.push(question)
    }
  })

  // 添加之前已选择的题目（不在当前页面的）
  if (props.selectedQuestions) {
    props.selectedQuestions.forEach(question => {
      if (selectedQuestionIds.value.includes(question.question_id) &&
          !allSelectedQuestions.find(q => q.question_id === question.question_id)) {
        allSelectedQuestions.push(question)
      }
    })
  }

  emit('confirm', allSelectedQuestions)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.filters {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.question-list {
  max-height: 600px;
}

.list-header {
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 15px;
}

.questions {
  max-height: 400px;
  overflow-y: auto;
}

.question-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.question-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.question-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.question-content {
  margin-left: 30px;
}

.question-text {
  font-size: 14px;
  color: #303133;
  margin-bottom: 10px;
  line-height: 1.5;
}

.options {
  margin-top: 10px;
}

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  margin: 5px 0;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 13px;
}

.option.correct {
  background: #f0f9ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.correct-icon {
  color: #67c23a;
}

.answer {
  margin-top: 10px;
  font-size: 13px;
  color: #606266;
}

.answer-text {
  color: #303133;
  font-weight: 500;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.no-course-tip,
.no-questions-tip {
  padding: 40px 20px;
  text-align: center;
}
</style>
