<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑题目' : '新建题目'"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="课程" prop="course_id">
        <el-select v-model="form.course_id" placeholder="选择课程" style="width: 100%">
          <el-option
            v-for="course in courses"
            :key="course.course_id"
            :label="course.course_name"
            :value="course.course_id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="题型" prop="question_type">
        <el-select v-model="form.question_type" placeholder="选择题型" style="width: 100%" @change="handleTypeChange">
          <el-option label="单选题" value="single_choice" />
          <el-option label="多选题" value="multiple_choice" />
          <el-option label="判断题" value="true_false" />
          <el-option label="填空题" value="fill_in_blank" />
          <el-option label="简答题" value="essay" />
        </el-select>
      </el-form-item>

      <el-form-item label="难度" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="选择难度" style="width: 100%">
          <el-option label="简单" :value="1" />
          <el-option label="中等" :value="2" />
          <el-option label="困难" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="题目内容" prop="question_text">
        <el-input
          v-model="form.question_text"
          type="textarea"
          :rows="4"
          placeholder="请输入题目内容"
        />
      </el-form-item>

      <!-- 选择题和判断题的选项 -->
      <div v-if="needOptions" class="options-section">
        <el-form-item label="选项">
          <div class="options-container">
            <div
              v-for="(option, index) in form.options"
              :key="index"
              class="option-item"
            >
              <el-checkbox
                v-model="option.is_correct"
                :disabled="form.question_type === 'single_choice' && getSingleChoiceCorrectCount() >= 1 && !option.is_correct"
                @change="handleOptionCorrectChange(index)"
              >
                正确答案
              </el-checkbox>
              <el-input
                v-model="option.option_text"
                placeholder="请输入选项内容"
                style="flex: 1; margin: 0 10px"
              />
              <el-button
                v-if="form.options.length > 2"
                type="danger"
                size="small"
                @click="removeOption(index)"
              >
                删除
              </el-button>
            </div>
            <el-button
              v-if="form.options.length < 6"
              type="primary"
              size="small"
              @click="addOption"
            >
              添加选项
            </el-button>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { questionApi } from '@/api'
import type { Question, Course, CreateQuestionRequest, UpdateQuestionRequest, QuestionOption } from '@/types/api'

// Props
interface Props {
  visible: boolean
  courses: Course[]
  question?: Question | null
}

const props = withDefaults(defineProps<Props>(), {
  question: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const form = reactive<CreateQuestionRequest & { options: QuestionOption[] }>({
  course_id: 0,
  question_text: '',
  question_type: 'single_choice',
  difficulty: 2,
  options: [
    { option_text: '', is_correct: false },
    { option_text: '', is_correct: false }
  ]
})

// 计算属性
const isEdit = computed(() => !!props.question)
const needOptions = computed(() =>
  ['single_choice', 'multiple_choice', 'true_false'].includes(form.question_type)
)

// 表单验证规则
const rules: FormRules = {
  course_id: [
    { required: true, message: '请选择课程', trigger: 'change' }
  ],
  question_type: [
    { required: true, message: '请选择题型', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度', trigger: 'change' }
  ],
  question_text: [
    { required: true, message: '请输入题目内容', trigger: 'blur' },
    { min: 5, message: '题目内容至少5个字符', trigger: 'blur' }
  ]
}

// 监听visible变化，初始化表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// 初始化表单
const initForm = () => {
  if (props.question) {
    // 编辑模式
    form.course_id = props.question.course_id
    form.question_text = props.question.question_text
    form.question_type = props.question.question_type
    form.difficulty = props.question.difficulty
    form.options = props.question.options?.map(opt => ({
      option_text: opt.option_text,
      is_correct: opt.is_correct
    })) || []
  } else {
    // 新建模式
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  form.course_id = 0
  form.question_text = ''
  form.question_type = 'single_choice'
  form.difficulty = 2
  form.options = [
    { option_text: '', is_correct: false },
    { option_text: '', is_correct: false }
  ]
}

// 题型变化处理
const handleTypeChange = (type: string) => {
  if (type === 'true_false') {
    // 判断题固定两个选项
    form.options = [
      { option_text: '正确', is_correct: false },
      { option_text: '错误', is_correct: false }
    ]
  } else if (['single_choice', 'multiple_choice'].includes(type)) {
    // 选择题至少两个选项
    if (form.options.length < 2) {
      form.options = [
        { option_text: '', is_correct: false },
        { option_text: '', is_correct: false }
      ]
    }
  }
}

// 添加选项
const addOption = () => {
  if (form.options.length < 6) {
    form.options.push({ option_text: '', is_correct: false })
  }
}

// 删除选项
const removeOption = (index: number) => {
  if (form.options.length > 2) {
    form.options.splice(index, 1)
  }
}

// 获取单选题正确答案数量
const getSingleChoiceCorrectCount = () => {
  return form.options.filter(opt => opt.is_correct).length
}

// 选项正确答案变化处理
const handleOptionCorrectChange = (index: number) => {
  if (form.question_type === 'single_choice') {
    // 单选题只能有一个正确答案
    if (form.options[index].is_correct) {
      form.options.forEach((opt, i) => {
        if (i !== index) {
          opt.is_correct = false
        }
      })
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证选择题必须有正确答案
    if (needOptions.value) {
      const hasCorrectAnswer = form.options.some(opt => opt.is_correct)
      if (!hasCorrectAnswer) {
        ElMessage.error('请设置至少一个正确答案')
        return
      }

      // 验证选项内容不能为空
      const hasEmptyOption = form.options.some(opt => !opt.option_text.trim())
      if (hasEmptyOption) {
        ElMessage.error('选项内容不能为空')
        return
      }
    }

    submitting.value = true

    const submitData = {
      course_id: form.course_id,
      question_text: form.question_text,
      question_type: form.question_type,
      difficulty: form.difficulty,
      options: needOptions.value ? form.options : undefined
    }

    if (isEdit.value && props.question) {
      await questionApi.updateQuestion(props.question.question_id, submitData as UpdateQuestionRequest)
      ElMessage.success('题目更新成功')
    } else {
      await questionApi.createQuestion(submitData as CreateQuestionRequest)
      ElMessage.success('题目创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}
</script>

<style scoped>
.options-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
