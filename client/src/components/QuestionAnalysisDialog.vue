<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="题目详细分析"
    width="800px"
    @close="handleClose"
  >
    <div v-if="question" class="question-analysis">
      <!-- 题目信息 -->
      <div class="question-info">
        <el-card>
          <template #header>
            <span>题目信息</span>
          </template>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">题型：</span>
              <span class="value">{{ getQuestionTypeText(question.question_type) }}</span>
            </div>
            <div class="info-item">
              <span class="label">总分：</span>
              <span class="value">{{ question.total_score }}分</span>
            </div>
            <div class="info-item">
              <span class="label">作答人数：</span>
              <span class="value">{{ question.total_answers }}人</span>
            </div>
            <div class="info-item">
              <span class="label">正确人数：</span>
              <span class="value">{{ question.correct_count }}人</span>
            </div>
            <div class="info-item">
              <span class="label">正确率：</span>
              <span class="value">
                <el-tag :type="getCorrectRateColor(question.correct_rate)">
                  {{ question.correct_rate }}%
                </el-tag>
              </span>
            </div>
            <div class="info-item">
              <span class="label">平均得分：</span>
              <span class="value">{{ question.average_score.toFixed(1) }}分</span>
            </div>
          </div>
          <div class="question-content">
            <div class="question-text" v-html="question.question_text"></div>
          </div>
        </el-card>
      </div>

      <!-- 选项分析（仅选择题） -->
      <div v-if="question.option_analysis" class="option-analysis">
        <el-card>
          <template #header>
            <span>选项分析</span>
          </template>
          <div class="options-chart">
            <div
              v-for="(option, index) in question.option_analysis"
              :key="index"
              class="option-item"
              :class="{ 'correct-option': option.is_correct }"
            >
              <div class="option-header">
                <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                <span class="option-text">{{ option.option_text }}</span>
                <el-tag v-if="option.is_correct" type="success" size="small">正确答案</el-tag>
              </div>
              <div class="option-stats">
                <div class="stats-bar">
                  <div
                    class="stats-fill"
                    :style="{
                      width: `${option.selected_rate}%`,
                      backgroundColor: option.is_correct ? '#67c23a' : '#409eff'
                    }"
                  ></div>
                </div>
                <div class="stats-text">
                  {{ option.selected_count }}人选择 ({{ option.selected_rate }}%)
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 分析建议 -->
      <div class="analysis-suggestions">
        <el-card>
          <template #header>
            <span>分析建议</span>
          </template>
          <div class="suggestions-content">
            <el-alert
              v-if="question.correct_rate < 50"
              title="正确率偏低"
              type="warning"
              :description="getLowCorrectRateSuggestion()"
              show-icon
              :closable="false"
            />
            <el-alert
              v-else-if="question.correct_rate < 70"
              title="正确率中等"
              type="info"
              :description="getMediumCorrectRateSuggestion()"
              show-icon
              :closable="false"
            />
            <el-alert
              v-else
              title="正确率良好"
              type="success"
              :description="getHighCorrectRateSuggestion()"
              show-icon
              :closable="false"
            />

            <!-- 选择题特殊建议 -->
            <div v-if="question.option_analysis" class="option-suggestions">
              <h4>选项分析建议：</h4>
              <ul>
                <li v-for="suggestion in getOptionSuggestions()" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { QuestionAnalysis } from '@/types/api'

// Props
interface Props {
  visible: boolean
  question?: QuestionAnalysis | null
}

const props = withDefaults(defineProps<Props>(), {
  question: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 工具函数
const getQuestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getCorrectRateColor = (rate: number) => {
  if (rate >= 80) return 'success'
  if (rate >= 60) return 'warning'
  return 'danger'
}

const getLowCorrectRateSuggestion = () => {
  return '该题目正确率较低，建议：1. 检查题目表述是否清晰；2. 考虑是否需要在课堂上重点讲解相关知识点；3. 提供更多练习题帮助学生理解。'
}

const getMediumCorrectRateSuggestion = () => {
  return '该题目正确率中等，建议：1. 分析错误选项的选择情况；2. 在复习时重点强调相关概念；3. 可以作为典型例题进行讲解。'
}

const getHighCorrectRateSuggestion = () => {
  return '该题目正确率良好，说明学生对相关知识点掌握较好。可以考虑：1. 适当增加题目难度；2. 设计更有挑战性的变式题目。'
}

const getOptionSuggestions = () => {
  if (!props.question?.option_analysis) return []

  const suggestions: string[] = []
  const options = props.question.option_analysis

  // 找出最容易混淆的错误选项
  const wrongOptions = options.filter(opt => !opt.is_correct && opt.selected_rate > 20)
  if (wrongOptions.length > 0) {
    suggestions.push(`选项${wrongOptions.map(opt => String.fromCharCode(65 + options.indexOf(opt))).join('、')}容易被误选，建议重点讲解这些选项的错误原因`)
  }

  // 检查是否有选项选择率过低
  const lowSelectedOptions = options.filter(opt => opt.selected_rate < 5)
  if (lowSelectedOptions.length > 0) {
    suggestions.push('部分选项选择率过低，可能存在明显错误，建议优化选项设计')
  }

  // 检查正确选项选择率
  const correctOption = options.find(opt => opt.is_correct)
  if (correctOption && correctOption.selected_rate < 60) {
    suggestions.push('正确选项选择率偏低，建议检查题目难度和选项设计')
  }

  return suggestions
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.question-analysis {
  max-height: 70vh;
  overflow-y: auto;
}

.question-info {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
}

.question-content {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.question-text {
  line-height: 1.6;
  color: #303133;
}

.option-analysis {
  margin-bottom: 20px;
}

.option-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.correct-option {
  border-color: #67c23a;
  background: #f0f9ff;
}

.option-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.option-label {
  font-weight: bold;
  margin-right: 8px;
  color: #409eff;
}

.option-text {
  flex: 1;
  margin-right: 10px;
}

.option-stats {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stats-bar {
  flex: 1;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.stats-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.stats-text {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.suggestions-content {
  space-y: 15px;
}

.suggestions-content > * {
  margin-bottom: 15px;
}

.option-suggestions {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.option-suggestions h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.option-suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.option-suggestions li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}
</style>
