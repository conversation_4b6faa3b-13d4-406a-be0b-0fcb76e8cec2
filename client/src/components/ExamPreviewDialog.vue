<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="试卷预览"
    width="1000px"
    @close="handleClose"
  >
    <div v-if="exam" class="exam-preview">
      <!-- 试卷基本信息 -->
      <div class="exam-info">
        <h2>{{ exam.exam_name }}</h2>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">课程：</span>
            <span class="value">{{ exam.course?.course_name }}</span>
          </div>
          <div class="info-item">
            <span class="label">总分：</span>
            <span class="value">{{ exam.total_score }} 分</span>
          </div>
          <div class="info-item">
            <span class="label">考试时长：</span>
            <span class="value">{{ exam.duration }} 分钟</span>
          </div>
          <div class="info-item">
            <span class="label">题目数量：</span>
            <span class="value">{{ exam.examQuestions?.length || 0 }} 题</span>
          </div>
          <div class="info-item">
            <span class="label">开始时间：</span>
            <span class="value">{{ formatDateTime(exam.start_time) }}</span>
          </div>
          <div class="info-item">
            <span class="label">结束时间：</span>
            <span class="value">{{ formatDateTime(exam.end_time) }}</span>
          </div>
        </div>
      </div>

      <!-- 题目列表 -->
      <div class="questions-section">
        <h3>试卷题目</h3>
        <div class="questions-list">
          <div
            v-for="(examQuestion, index) in exam.examQuestions"
            :key="examQuestion.exam_question_id"
            class="question-item"
          >
            <div class="question-header">
              <span class="question-number">{{ index + 1 }}.</span>
              <el-tag :type="getQuestionTypeColor(examQuestion.question?.question_type || '')" size="small">
                {{ getQuestionTypeName(examQuestion.question?.question_type || '') }}
              </el-tag>
              <el-tag :type="getDifficultyColor(examQuestion.question?.difficulty || 1)" size="small">
                {{ getDifficultyName(examQuestion.question?.difficulty || 1) }}
              </el-tag>
              <span class="question-score">{{ examQuestion.score }} 分</span>
            </div>

            <div class="question-content">
              <div class="question-text">{{ examQuestion.question?.question_text }}</div>

              <!-- 选择题选项 -->
              <div v-if="['single_choice', 'multiple_choice'].includes(examQuestion.question?.question_type || '')" class="options">
                <div
                  v-for="option in examQuestion.question?.options"
                  :key="option.option_id"
                  class="option"
                  :class="{ correct: option.is_correct }"
                >
                  <span class="option-label">{{ getOptionLabel(examQuestion.question?.options?.indexOf(option) || 0) }}.</span>
                  <span class="option-text">{{ option.option_text }}</span>
                  <el-icon v-if="option.is_correct" class="correct-icon">
                    <Check />
                  </el-icon>
                </div>
              </div>

              <!-- 判断题答案 -->
              <div v-else-if="examQuestion.question?.question_type === 'true_false'" class="answer-section">
                <div class="answer-label">正确答案：</div>
                <el-tag :type="examQuestion.question?.correct_answer === 'true' ? 'success' : 'danger'" size="small">
                  {{ examQuestion.question?.correct_answer === 'true' ? '正确' : '错误' }}
                </el-tag>
              </div>

              <!-- 填空题和简答题答案 -->
              <div v-else-if="['fill_in_blank', 'essay'].includes(examQuestion.question?.question_type || '')" class="answer-section">
                <div class="answer-label">参考答案：</div>
                <div class="answer-content">{{ examQuestion.question?.correct_answer }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="statistics">
        <h3>题型统计</h3>
        <div class="stats-grid">
          <div
            v-for="stat in questionTypeStats"
            :key="stat.type"
            class="stat-item"
          >
            <div class="stat-label">{{ stat.name }}</div>
            <div class="stat-value">{{ stat.count }} 题 ({{ stat.totalScore }} 分)</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handlePrint">打印试卷</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Check } from '@element-plus/icons-vue'
import type { Exam } from '@/types/api'

// Props
interface Props {
  visible: boolean
  exam?: Exam | null
}

const props = withDefaults(defineProps<Props>(), {
  exam: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 计算属性 - 题型统计
const questionTypeStats = computed(() => {
  if (!props.exam?.examQuestions) return []

  const stats: Record<string, { count: number; totalScore: number }> = {}

  props.exam.examQuestions.forEach(eq => {
    const type = eq.question?.question_type || ''
    if (!stats[type]) {
      stats[type] = { count: 0, totalScore: 0 }
    }
    stats[type].count++
    stats[type].totalScore += eq.score
  })

  return Object.entries(stats).map(([type, data]) => ({
    type,
    name: getQuestionTypeName(type),
    count: data.count,
    totalScore: data.totalScore
  }))
})

// 工具函数
const getQuestionTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getQuestionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    single_choice: 'primary',
    multiple_choice: 'success',
    true_false: 'warning',
    fill_in_blank: 'info',
    essay: 'danger'
  }
  return colorMap[type] || ''
}

const getDifficultyName = (difficulty: number) => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

const getDifficultyColor = (difficulty: number) => {
  const colorMap: Record<number, string> = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return colorMap[difficulty] || ''
}

const getOptionLabel = (index: number) => {
  return String.fromCharCode(65 + index) // A, B, C, D...
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 打印试卷
const handlePrint = () => {
  window.print()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.exam-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.exam-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.exam-info h2 {
  margin: 0 0 15px 0;
  color: #303133;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
}

.questions-section h3,
.statistics h3 {
  color: #303133;
  margin-bottom: 15px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.questions-list {
  space-y: 15px;
}

.question-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  background: white;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.question-number {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.question-score {
  margin-left: auto;
  font-weight: 500;
  color: #e6a23c;
}

.question-content {
  margin-left: 10px;
}

.question-text {
  font-size: 15px;
  color: #303133;
  line-height: 1.6;
  margin-bottom: 15px;
}

.options {
  margin-top: 10px;
}

.option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 8px 0;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.option.correct {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.option-label {
  font-weight: 500;
  margin-right: 8px;
  min-width: 20px;
}

.option-text {
  flex: 1;
}

.correct-icon {
  color: #67c23a;
  margin-left: 8px;
}

.answer-section {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.answer-label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
}

.answer-content {
  color: #303133;
  line-height: 1.5;
}

.statistics {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-item {
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  text-align: center;
}

.stat-label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  text-align: right;
}

/* 打印样式 */
@media print {
  .dialog-footer {
    display: none;
  }

  .exam-preview {
    max-height: none;
    overflow: visible;
  }
}
</style>
