<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="题目详情"
    width="700px"
    @close="handleClose"
  >
    <div v-if="question" class="question-detail">
      <!-- 基本信息 -->
      <div class="info-section">
        <h3>基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="题目ID">{{ question.question_id }}</el-descriptions-item>
          <el-descriptions-item label="课程">{{ question.course?.course_name }}</el-descriptions-item>
          <el-descriptions-item label="题型">
            <el-tag :type="getQuestionTypeColor(question.question_type)">
              {{ getQuestionTypeName(question.question_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="难度">
            <el-tag :type="getDifficultyColor(question.difficulty)">
              {{ getDifficultyName(question.difficulty) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建者">{{ question.creator?.full_name }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(question.create_time) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 题目内容 -->
      <div class="content-section">
        <h3>题目内容</h3>
        <div class="question-text">
          {{ question.question_text }}
        </div>
      </div>

      <!-- 选项（如果有） -->
      <div v-if="question.options && question.options.length > 0" class="options-section">
        <h3>选项</h3>
        <div class="options-list">
          <div
            v-for="(option, index) in question.options"
            :key="option.option_id"
            class="option-item"
            :class="{ 'correct-option': option.is_correct }"
          >
            <span class="option-label">{{ getOptionLabel(index) }}.</span>
            <span class="option-text">{{ option.option_text }}</span>
            <el-tag v-if="option.is_correct" type="success" size="small">正确答案</el-tag>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editQuestion">编辑题目</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Question } from '@/types/api'

// Props
interface Props {
  visible: boolean
  question?: Question | null
}

const props = withDefaults(defineProps<Props>(), {
  question: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'edit': [question: Question]
}>()

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 工具函数
const getQuestionTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getQuestionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    single_choice: 'primary',
    multiple_choice: 'success',
    true_false: 'warning',
    fill_in_blank: 'info',
    essay: 'danger'
  }
  return colorMap[type] || ''
}

const getDifficultyName = (difficulty: number) => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

const getDifficultyColor = (difficulty: number) => {
  const colorMap: Record<number, string> = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return colorMap[difficulty] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getOptionLabel = (index: number) => {
  return String.fromCharCode(65 + index) // A, B, C, D...
}

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const editQuestion = () => {
  if (props.question) {
    emit('edit', props.question)
    handleClose()
  }
}
</script>

<style scoped>
.question-detail {
  padding: 10px 0;
}

.info-section,
.content-section,
.options-section {
  margin-bottom: 25px;
}

.info-section h3,
.content-section h3,
.options-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.question-text {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  line-height: 1.6;
  font-size: 14px;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.option-item.correct-option {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.option-label {
  font-weight: 600;
  color: #606266;
  margin-right: 10px;
  min-width: 20px;
}

.option-text {
  flex: 1;
  color: #303133;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}
</style>
