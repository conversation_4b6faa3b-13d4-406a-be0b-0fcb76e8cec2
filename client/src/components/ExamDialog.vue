<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑试卷' : '创建试卷'"
    width="1000px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="试卷名称" prop="exam_name">
            <el-input v-model="form.exam_name" placeholder="请输入试卷名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程" prop="course_id">
            <el-select v-model="form.course_id" placeholder="选择课程" style="width: 100%" @change="handleCourseChange">
              <el-option
                v-for="course in courses"
                :key="course.course_id"
                :label="course.course_name"
                :value="course.course_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="开始时间" prop="start_time">
            <el-date-picker
              v-model="form.start_time"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="calculateDuration"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结束时间" prop="end_time">
            <el-date-picker
              v-model="form.end_time"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="calculateDuration"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="考试时长(分钟)">
            <el-input
              :model-value="durationDisplay"
              readonly
              placeholder="自动计算"
              style="width: 100%"
            />
            <div class="duration-tip">
              <el-text type="info" size="small">根据开始和结束时间自动计算</el-text>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="总分" prop="total_score">
        <el-input-number
          v-model="form.total_score"
          :min="1"
          :max="1000"
          placeholder="试卷总分"
          style="width: 200px"
        />
      </el-form-item>

      <!-- 组卷方式选择 -->
      <el-form-item label="组卷方式">
        <el-radio-group v-model="compositionMode" @change="handleModeChange">
          <el-radio value="manual">手动组卷</el-radio>
          <el-radio value="auto">自动组卷</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 自动组卷配置 -->
      <div v-if="compositionMode === 'auto'" class="auto-composition">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="题目数量">
              <el-input-number
                v-model="autoConfig.question_count"
                :min="1"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="题型">
              <el-select v-model="autoConfig.question_types" multiple placeholder="选择题型" style="width: 100%">
                <el-option label="单选题" value="single_choice" />
                <el-option label="多选题" value="multiple_choice" />
                <el-option label="判断题" value="true_false" />
                <el-option label="填空题" value="fill_in_blank" />
                <el-option label="简答题" value="essay" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="难度">
              <el-select v-model="autoConfig.difficulties" multiple placeholder="选择难度" style="width: 100%">
                <el-option label="简单" :value="1" />
                <el-option label="中等" :value="2" />
                <el-option label="困难" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="generateQuestions" :loading="generating">
            生成题目
          </el-button>
        </el-form-item>
      </div>

      <!-- 手动组卷 - 题目选择 -->
      <div v-if="compositionMode === 'manual'" class="manual-composition">
        <el-form-item label="选择题目">
          <el-button
            type="primary"
            @click="handleSelectQuestions"
            :disabled="!form.course_id"
          >
            选择题目 (已选 {{ selectedQuestions.length }} 题)
          </el-button>
          <div v-if="!form.course_id" class="course-tip">
            <el-text type="info" size="small">请先选择课程</el-text>
          </div>
        </el-form-item>
      </div>

      <!-- 已选题目列表 -->
      <div v-if="selectedQuestions.length > 0" class="selected-questions">
        <el-form-item label="已选题目">
          <div class="questions-list">
            <div
              v-for="(item, index) in selectedQuestions"
              :key="item.question_id"
              class="question-item"
            >
              <div class="question-info">
                <span class="question-number">{{ index + 1 }}.</span>
                <span class="question-text">{{ item.question_text.substring(0, 50) }}...</span>
                <el-tag :type="getQuestionTypeColor(item.question_type)" size="small">
                  {{ getQuestionTypeName(item.question_type) }}
                </el-tag>
              </div>
              <div class="question-score">
                <el-input-number
                  v-model="item.score"
                  :min="0.5"
                  :max="100"
                  :step="0.5"
                  size="small"
                  style="width: 100px"
                />
                <el-button
                  type="danger"
                  size="small"
                  @click="removeQuestion(index)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <!-- 题目选择器对话框 -->
    <QuestionSelector
      v-model:visible="showQuestionSelector"
      :course-id="form.course_id"
      :selected-questions="selectedQuestions"
      @confirm="handleQuestionsSelected"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { examApi } from '@/api'
import type {
  Exam,
  Course,
  CreateExamRequest,
  UpdateExamRequest,
  Question,
  AutoGenerateExamRequest
} from '@/types/api'
import QuestionSelector from './QuestionSelector.vue'

// Props
interface Props {
  visible: boolean
  courses: Course[]
  exam?: Exam | null
}

const props = withDefaults(defineProps<Props>(), {
  exam: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const generating = ref(false)
const compositionMode = ref<'manual' | 'auto'>('manual')
const showQuestionSelector = ref(false)
const selectedQuestions = ref<(Question & { score: number })[]>([])

// 表单数据
const form = reactive<CreateExamRequest>({
  exam_name: '',
  course_id: 0,
  start_time: '',
  end_time: '',
  duration: 120,
  total_score: 100,
  questions: []
})

// 自动组卷配置
const autoConfig = reactive<AutoGenerateExamRequest>({
  course_id: 0,
  question_types: [],
  difficulties: [],
  question_count: 20,
  total_score: 100
})

// 计算属性
const isEdit = computed(() => !!props.exam)

// 计算考试时长显示
const durationDisplay = computed(() => {
  if (!form.start_time || !form.end_time) {
    return '请先选择开始和结束时间'
  }

  const startTime = new Date(form.start_time)
  const endTime = new Date(form.end_time)

  if (endTime <= startTime) {
    return '结束时间必须晚于开始时间'
  }

  const diffMs = endTime.getTime() - startTime.getTime()
  const diffMinutes = Math.round(diffMs / (1000 * 60))

  return `${diffMinutes} 分钟`
})

// 计算考试时长（分钟数）
const calculateDuration = () => {
  if (!form.start_time || !form.end_time) {
    form.duration = 0
    return
  }

  const startTime = new Date(form.start_time)
  const endTime = new Date(form.end_time)

  if (endTime <= startTime) {
    form.duration = 0
    return
  }

  const diffMs = endTime.getTime() - startTime.getTime()
  const diffMinutes = Math.round(diffMs / (1000 * 60))

  form.duration = diffMinutes
}

// 表单验证规则
const rules: FormRules = {
  exam_name: [
    { required: true, message: '请输入试卷名称', trigger: 'blur' }
  ],
  course_id: [
    { required: true, message: '请选择课程', trigger: 'change' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value || !form.start_time) {
          callback()
          return
        }
        const startTime = new Date(form.start_time)
        const endTime = new Date(value)
        if (endTime <= startTime) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  total_score: [
    { required: true, message: '请输入总分', trigger: 'blur' }
  ]
}

// 监听visible变化，初始化表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// 初始化表单
const initForm = () => {
  if (props.exam) {
    // 编辑模式
    form.exam_name = props.exam.exam_name
    form.course_id = props.exam.course_id
    form.start_time = props.exam.start_time
    form.end_time = props.exam.end_time
    form.duration = props.exam.duration
    form.total_score = props.exam.total_score

    // 重新计算时长（确保一致性）
    calculateDuration()

    // 加载已选题目
    if (props.exam.examQuestions) {
      selectedQuestions.value = props.exam.examQuestions.map(eq => ({
        ...eq.question!,
        score: eq.score
      }))
    }
  } else {
    // 新建模式
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  form.exam_name = ''
  form.course_id = 0
  form.start_time = ''
  form.end_time = ''
  form.duration = 120
  form.total_score = 100
  selectedQuestions.value = []
  compositionMode.value = 'manual'
}

// 课程变化处理
const handleCourseChange = (courseId: number) => {
  autoConfig.course_id = courseId
  selectedQuestions.value = []
}

// 组卷方式变化处理
const handleModeChange = () => {
  selectedQuestions.value = []
}

// 自动生成题目
const generateQuestions = async () => {
  if (!form.course_id) {
    ElMessage.error('请先选择课程')
    return
  }

  try {
    generating.value = true
    const response = await examApi.autoGenerateExam({
      ...autoConfig,
      course_id: form.course_id,
      total_score: form.total_score
    })

    selectedQuestions.value = response.data.questions
    ElMessage.success(`成功生成 ${response.data.questions.length} 道题目`)
  } catch (error) {
    console.error('自动生成题目失败:', error)
    ElMessage.error('自动生成题目失败')
  } finally {
    generating.value = false
  }
}

// 选择题目按钮点击
const handleSelectQuestions = () => {
  if (!form.course_id) {
    ElMessage.warning('请先选择课程')
    return
  }
  showQuestionSelector.value = true
}

// 题目选择确认
const handleQuestionsSelected = (questions: Question[]) => {
  const scorePerQuestion = (form.total_score || 100) / questions.length
  selectedQuestions.value = questions.map(q => ({
    ...q,
    score: Math.round(scorePerQuestion * 10) / 10
  }))
}

// 删除题目
const removeQuestion = (index: number) => {
  selectedQuestions.value.splice(index, 1)
}

// 工具函数
const getQuestionTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getQuestionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    single_choice: 'primary',
    multiple_choice: 'success',
    true_false: 'warning',
    fill_in_blank: 'info',
    essay: 'danger'
  }
  return colorMap[type] || ''
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (selectedQuestions.value.length === 0) {
      ElMessage.error('请至少选择一道题目')
      return
    }

    submitting.value = true

    const submitData = {
      ...form,
      questions: selectedQuestions.value.map(q => ({
        question_id: q.question_id,
        score: q.score
      }))
    }

    if (isEdit.value && props.exam) {
      await examApi.updateExam(props.exam.exam_id, submitData as UpdateExamRequest)
      ElMessage.success('试卷更新成功')
    } else {
      await examApi.createExam(submitData)
      ElMessage.success('试卷创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}
</script>

<style scoped>
.auto-composition,
.manual-composition {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
  margin-bottom: 15px;
}

.selected-questions {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.questions-list {
  max-height: 300px;
  overflow-y: auto;
}

.question-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: white;
}

.question-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.question-number {
  font-weight: bold;
  color: #409eff;
}

.question-text {
  flex: 1;
  color: #303133;
}

.question-score {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}

.course-tip {
  margin-top: 5px;
}

.duration-tip {
  margin-top: 5px;
}
</style>
