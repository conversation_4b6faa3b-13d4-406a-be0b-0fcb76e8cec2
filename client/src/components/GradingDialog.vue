<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="答卷批阅"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="answerSheet" class="grading-dialog">
      <!-- 学生信息 -->
      <div class="student-info">
        <el-card>
          <template #header>
            <span>学生信息</span>
          </template>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ answerSheet.student.full_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">用户名：</span>
              <span class="value">{{ answerSheet.student.username }}</span>
            </div>
            <div class="info-item">
              <span class="label">考试：</span>
              <span class="value">{{ answerSheet.exam.exam_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">课程：</span>
              <span class="value">{{ answerSheet.exam.course.course_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">提交时间：</span>
              <span class="value">{{ formatDateTime(answerSheet.submission_time) }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前得分：</span>
              <span class="value">{{ getCurrentScore() }} / {{ answerSheet.exam.total_score }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 答题详情 -->
      <div class="questions-section">
        <el-card>
          <template #header>
            <div class="questions-header">
              <span>答题详情</span>
              <div class="progress-info">
                <span>批阅进度: {{ getGradedCount() }} / {{ getSubjectiveCount() }}</span>
              </div>
            </div>
          </template>

          <div class="questions-list">
            <div
              v-for="(examQuestion, index) in answerSheet.exam.examQuestions"
              :key="examQuestion.exam_question_id"
              class="question-item"
            >
              <div class="question-header">
                <div class="question-title">
                  <span class="question-number">{{ index + 1 }}.</span>
                  <span class="question-type">{{ getQuestionTypeText(examQuestion.question.question_type) }}</span>
                  <span class="question-score">({{ examQuestion.score }}分)</span>
                </div>
                <div v-if="isSubjectiveQuestion(examQuestion.question.question_type)" class="grading-status">
                  <el-tag
                    :type="getGradingStatusColor(examQuestion.question.question_id)"
                    size="small"
                  >
                    {{ getGradingStatusText(examQuestion.question.question_id) }}
                  </el-tag>
                </div>
              </div>

              <div class="question-content">
                <div class="question-text" v-html="examQuestion.question.question_text"></div>

                <!-- 选择题选项 -->
                <div v-if="examQuestion.question.options?.length" class="question-options">
                  <div
                    v-for="option in examQuestion.question.options"
                    :key="option.option_id"
                    class="option-item"
                  >
                    <span class="option-label">{{ String.fromCharCode(65 + (option.option_id || 0)) }}.</span>
                    <span class="option-text">{{ option.option_text }}</span>
                  </div>
                </div>

                <!-- 学生答案 -->
                <div class="student-answer">
                  <div class="answer-label">学生答案：</div>
                  <div class="answer-content">
                    {{ getStudentAnswer(examQuestion.question.question_id) || '未作答' }}
                  </div>
                </div>

                <!-- 正确答案（客观题） -->
                <div
                  v-if="!isSubjectiveQuestion(examQuestion.question.question_type)"
                  class="correct-answer"
                >
                  <div class="answer-label">正确答案：</div>
                  <div class="answer-content">
                    {{ examQuestion.question.correct_answer }}
                  </div>
                </div>

                <!-- 主观题批阅区域 -->
                <div
                  v-if="isSubjectiveQuestion(examQuestion.question.question_type)"
                  class="grading-area"
                >
                  <el-form :model="gradingForm" inline>
                    <el-form-item label="得分">
                      <el-input-number
                        v-model="gradingForm[examQuestion.question.question_id].score"
                        :min="0"
                        :max="examQuestion.score"
                        :precision="1"
                        style="width: 120px"
                      />
                      <span class="score-suffix">/ {{ examQuestion.score }} 分</span>
                    </el-form-item>
                    <el-form-item label="评语">
                      <el-input
                        v-model="gradingForm[examQuestion.question.question_id].comment"
                        placeholder="可选评语"
                        style="width: 300px"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        type="primary"
                        size="small"
                        @click="saveQuestionScore(examQuestion.question.question_id)"
                      >
                        保存
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 已批阅信息 -->
                <div
                  v-if="getAnswerInfo(examQuestion.question.question_id)?.is_marked"
                  class="graded-info"
                >
                  <div class="graded-score">
                    得分: {{ getAnswerInfo(examQuestion.question.question_id)?.score }} / {{ examQuestion.score }}
                  </div>
                  <div
                    v-if="getAnswerInfo(examQuestion.question.question_id)?.comment"
                    class="graded-comment"
                  >
                    评语: {{ getAnswerInfo(examQuestion.question.question_id)?.comment }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="success"
          :disabled="!canComplete()"
          @click="completeGrading"
        >
          完成批阅
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { gradingApi } from '@/api'
import type { AnswerSheetDetail, StudentAnswer } from '@/types/api'

// Props
interface Props {
  visible: boolean
  scoreId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  scoreId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const loading = ref(false)
const answerSheet = ref<AnswerSheetDetail | null>(null)
const gradingForm = reactive<Record<number, { score: number; comment: string }>>({})

// 监听visible和scoreId变化
watch([() => props.visible, () => props.scoreId], ([newVisible, newScoreId]) => {
  if (newVisible && newScoreId) {
    loadAnswerSheet()
  }
})

// 加载答卷详情
const loadAnswerSheet = async () => {
  if (!props.scoreId) return

  try {
    loading.value = true
    const response = await gradingApi.getAnswerSheet(props.scoreId)
    answerSheet.value = response.data

    // 初始化批阅表单
    initGradingForm()
  } catch (error) {
    console.error('加载答卷详情失败:', error)
    ElMessage.error('加载答卷详情失败')
  } finally {
    loading.value = false
  }
}

// 初始化批阅表单
const initGradingForm = () => {
  if (!answerSheet.value) return

  // 清空现有表单数据
  Object.keys(gradingForm).forEach(key => {
    delete gradingForm[parseInt(key)]
  })

  answerSheet.value.exam.examQuestions.forEach(examQuestion => {
    if (!examQuestion.question) return

    const questionId = examQuestion.question.question_id
    const answer = getAnswerInfo(questionId)

    gradingForm[questionId] = {
      score: answer?.score || 0,
      comment: answer?.comment || ''
    }
  })
}

// 获取学生答案信息
const getAnswerInfo = (questionId: number): StudentAnswer | undefined => {
  return answerSheet.value?.studentAnswers.find(answer => answer.question_id === questionId)
}

// 获取学生答案内容
const getStudentAnswer = (questionId: number): string => {
  const answer = getAnswerInfo(questionId)
  return answer?.student_answer || ''
}

// 保存单题分数
const saveQuestionScore = async (questionId: number) => {
  const answer = getAnswerInfo(questionId)
  if (!answer) return

  const formData = gradingForm[questionId]
  if (!formData) return

  try {
    await gradingApi.scoreQuestion({
      answer_id: answer.answer_id,
      score: formData.score,
      comment: formData.comment
    })

    ElMessage.success('保存成功')

    // 更新本地数据
    answer.score = formData.score
    answer.comment = formData.comment
    answer.is_marked = true
  } catch (error) {
    console.error('保存分数失败:', error)
    ElMessage.error('保存分数失败')
  }
}

// 完成批阅
const completeGrading = async () => {
  if (!props.scoreId) return

  try {
    await ElMessageBox.confirm(
      '确定要完成批阅吗？完成后将计算总分并更新状态。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await gradingApi.completeGrading(props.scoreId)
    ElMessage.success(`批阅完成，总分: ${response.data.total_score}`)

    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成批阅失败:', error)
      ElMessage.error('完成批阅失败')
    }
  }
}

// 工具函数
const formatDateTime = (dateString?: string) => {
  return dateString ? new Date(dateString).toLocaleString('zh-CN') : ''
}

const getQuestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const isSubjectiveQuestion = (type: string) => {
  return ['fill_in_blank', 'essay'].includes(type)
}

const getGradingStatusText = (questionId: number) => {
  const answer = getAnswerInfo(questionId)
  return answer?.is_marked ? '已批阅' : '待批阅'
}

const getGradingStatusColor = (questionId: number) => {
  const answer = getAnswerInfo(questionId)
  return answer?.is_marked ? 'success' : 'warning'
}

const getCurrentScore = () => {
  if (!answerSheet.value) return 0
  return answerSheet.value.studentAnswers.reduce((sum, answer) => sum + (answer.score || 0), 0)
}

const getSubjectiveCount = () => {
  if (!answerSheet.value) return 0
  return answerSheet.value.exam.examQuestions.filter(eq =>
    eq.question && isSubjectiveQuestion(eq.question.question_type)
  ).length
}

const getGradedCount = () => {
  if (!answerSheet.value) return 0
  return answerSheet.value.studentAnswers.filter(answer =>
    answer.is_marked &&
    answer.question &&
    isSubjectiveQuestion(answer.question.question_type)
  ).length
}

const canComplete = () => {
  return getGradedCount() === getSubjectiveCount()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  answerSheet.value = null
  Object.keys(gradingForm).forEach(key => {
    delete gradingForm[parseInt(key)]
  })
}
</script>

<style scoped>
.grading-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.student-info {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
}

.questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-info {
  font-size: 14px;
  color: #606266;
}

.question-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-number {
  font-weight: bold;
  color: #409eff;
}

.question-type {
  background: #f0f9ff;
  color: #409eff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.question-score {
  color: #e6a23c;
  font-weight: 500;
}

.question-text {
  margin-bottom: 15px;
  line-height: 1.6;
}

.question-options {
  margin-bottom: 15px;
}

.option-item {
  margin-bottom: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.option-label {
  font-weight: 500;
  margin-right: 8px;
}

.student-answer,
.correct-answer {
  margin-bottom: 15px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.answer-label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.answer-content {
  color: #303133;
  line-height: 1.6;
}

.grading-area {
  background: #fff7e6;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #ffd591;
  margin-bottom: 15px;
}

.score-suffix {
  margin-left: 8px;
  color: #606266;
}

.graded-info {
  background: #f0f9ff;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.graded-score {
  font-weight: 500;
  color: #409eff;
  margin-bottom: 5px;
}

.graded-comment {
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
