<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="发布考试"
    width="1000px"
    @close="handleClose"
  >
    <div v-if="exam" class="publish-dialog">
      <!-- 考试基本信息 -->
      <div class="exam-info">
        <h3>{{ exam.exam_name }}</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">课程：</span>
            <span class="value">{{ exam.course?.course_name }}</span>
          </div>
          <div class="info-item">
            <span class="label">考试时间：</span>
            <span class="value">{{ formatDateTime(exam.start_time) }} - {{ formatDateTime(exam.end_time) }}</span>
          </div>
          <div class="info-item">
            <span class="label">考试时长：</span>
            <span class="value">{{ exam.duration }} 分钟</span>
          </div>
          <div class="info-item">
            <span class="label">总分：</span>
            <span class="value">{{ exam.total_score }} 分</span>
          </div>
        </div>
      </div>

      <!-- 已发布学生列表 -->
      <div v-if="publishedStudents.length > 0" class="published-students">
        <h4>已发布学生 ({{ publishedStudents.length }})</h4>
        <div class="students-list">
          <el-tag
            v-for="student in publishedStudents"
            :key="student.student_id"
            :type="getStatusColor(student.status)"
            closable
            @close="removeStudent(student.student_id)"
          >
            {{ student.student?.full_name }} ({{ student.student?.username }})
            - {{ getStatusText(student.status) }}
          </el-tag>
        </div>
      </div>

      <!-- 学生选择 -->
      <div class="student-selection">
        <div class="selection-header">
          <h4>选择学生</h4>
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索学生姓名或用户名"
              style="width: 300px"
              clearable
              @input="searchStudents"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <div class="students-table">
          <el-table
            v-loading="loading"
            :data="availableStudents"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="username" label="用户名" width="150" />
            <el-table-column prop="full_name" label="姓名" width="150" />
            <el-table-column prop="email" label="邮箱" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              v-model:current-page="queryParams.page"
              v-model:page-size="queryParams.limit"
              :page-sizes="[10, 20, 50]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadStudents"
              @current-change="loadStudents"
            />
          </div>
        </div>
      </div>

      <!-- 考试统计 -->
      <div v-if="statistics" class="statistics">
        <h4>考试统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">总参与人数</div>
            <div class="stat-value">{{ statistics.total_students }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">待考试</div>
            <div class="stat-value">{{ statistics.pending_count }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">已提交</div>
            <div class="stat-value">{{ statistics.submitted_count }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">已批阅</div>
            <div class="stat-value">{{ statistics.marked_count }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">完成率</div>
            <div class="stat-value">{{ statistics.completion_rate }}%</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">平均分</div>
            <div class="stat-value">{{ statistics.average_score }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="publishing"
          :disabled="selectedStudents.length === 0"
          @click="handlePublish"
        >
          发布给选中学生 ({{ selectedStudents.length }})
        </el-button>
        <el-button
          v-if="publishedStudents.length > 0"
          type="danger"
          :loading="unpublishing"
          @click="handleUnpublishAll"
        >
          取消全部发布
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { examApi } from '@/api'
import type {
  Exam,
  StudentScore,
  ExamStatistics,
  PublishExamRequest
} from '@/types/api'

// Props
interface Props {
  visible: boolean
  exam?: Exam | null
}

const props = withDefaults(defineProps<Props>(), {
  exam: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const loading = ref(false)
const publishing = ref(false)
const unpublishing = ref(false)
const searchKeyword = ref('')
const publishedStudents = ref<StudentScore[]>([])
const availableStudents = ref<any[]>([])
const selectedStudents = ref<any[]>([])
const statistics = ref<ExamStatistics | null>(null)
const total = ref(0)

// 查询参数
const queryParams = reactive({
  page: 1,
  limit: 20,
  keyword: ''
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.exam) {
    loadData()
  }
})

// 加载数据
const loadData = async () => {
  await Promise.all([
    loadPublishedStudents(),
    loadStudents(),
    loadStatistics()
  ])
}

// 加载已发布学生
const loadPublishedStudents = async () => {
  if (!props.exam) return

  try {
    const response = await examApi.getExamStudents(props.exam.exam_id)
    publishedStudents.value = response.data
  } catch (error) {
    console.error('加载已发布学生失败:', error)
  }
}

// 加载可选学生
const loadStudents = async () => {
  try {
    loading.value = true
    const response = await examApi.getAvailableStudents(queryParams)
    availableStudents.value = response.data.students
    total.value = response.data.pagination.total
  } catch (error) {
    console.error('加载学生列表失败:', error)
    ElMessage.error('加载学生列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStatistics = async () => {
  if (!props.exam) return

  try {
    const response = await examApi.getExamStatistics(props.exam.exam_id)
    statistics.value = response.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 搜索学生
const searchStudents = () => {
  queryParams.keyword = searchKeyword.value
  queryParams.page = 1
  loadStudents()
}

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedStudents.value = selection
}

// 移除已发布学生
const removeStudent = async (studentId: number) => {
  if (!props.exam) return

  try {
    await ElMessageBox.confirm('确定要取消对该学生的发布吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await examApi.unpublishExam(props.exam.exam_id, { student_ids: [studentId] })
    ElMessage.success('取消发布成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消发布失败:', error)
      ElMessage.error('取消发布失败')
    }
  }
}

// 发布考试
const handlePublish = async () => {
  if (!props.exam || selectedStudents.value.length === 0) return

  try {
    publishing.value = true
    const studentIds = selectedStudents.value.map(s => s.user_id)

    await examApi.publishExam(props.exam.exam_id, { student_ids: studentIds })
    ElMessage.success(`成功发布给 ${studentIds.length} 名学生`)

    selectedStudents.value = []
    loadData()
    emit('success')
  } catch (error) {
    console.error('发布考试失败:', error)
    ElMessage.error('发布考试失败')
  } finally {
    publishing.value = false
  }
}

// 取消全部发布
const handleUnpublishAll = async () => {
  if (!props.exam) return

  try {
    await ElMessageBox.confirm('确定要取消全部发布吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    unpublishing.value = true
    await examApi.unpublishExam(props.exam.exam_id)
    ElMessage.success('取消全部发布成功')

    loadData()
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消全部发布失败:', error)
      ElMessage.error('取消全部发布失败')
    }
  } finally {
    unpublishing.value = false
  }
}

// 工具函数
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待考试',
    submitted: '已提交',
    marked: '已批阅'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'info',
    submitted: 'warning',
    marked: 'success'
  }
  return colorMap[status] || ''
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  selectedStudents.value = []
  searchKeyword.value = ''
  queryParams.keyword = ''
  queryParams.page = 1
}
</script>

<style scoped>
.publish-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.exam-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.exam-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
}

.published-students {
  margin-bottom: 20px;
}

.published-students h4 {
  color: #303133;
  margin-bottom: 10px;
}

.students-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.student-selection h4 {
  color: #303133;
  margin-bottom: 15px;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.students-table {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.statistics {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.statistics h4 {
  color: #303133;
  margin-bottom: 15px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-item {
  background: white;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
