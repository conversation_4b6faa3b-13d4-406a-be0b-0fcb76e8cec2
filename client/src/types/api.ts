// API 响应类型定义
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 用户相关类型
export interface User {
  user_id: number
  username: string
  full_name: string | null
  role: 'student' | 'teacher' | 'admin'
  email: string | null
  student_id: string | null
  create_time: string
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应类型
export interface LoginResponse {
  token: string
  user: User
}

// 分页参数
export interface PaginationParams {
  page?: number
  pageSize?: number
}

// 分页响应
export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 课程相关类型
export interface Course {
  course_id: number
  course_name: string
  description?: string
}

// 题目选项类型
export interface QuestionOption {
  option_id?: number
  option_text: string
  is_correct: boolean
}

// 题目类型
export interface Question {
  question_id: number
  course_id: number
  question_text: string
  question_type: 'single_choice' | 'multiple_choice' | 'true_false' | 'fill_in_blank' | 'essay'
  difficulty: number
  correct_answer?: string
  creator_id: number
  create_time: string
  course?: {
    course_name: string
  }
  creator?: {
    full_name: string
  }
  options?: QuestionOption[]
}

// 创建题目请求类型
export interface CreateQuestionRequest {
  course_id: number
  question_text: string
  question_type: 'single_choice' | 'multiple_choice' | 'true_false' | 'fill_in_blank' | 'essay'
  difficulty?: number
  options?: QuestionOption[]
}

// 更新题目请求类型
export interface UpdateQuestionRequest {
  course_id?: number
  question_text?: string
  question_type?: 'single_choice' | 'multiple_choice' | 'true_false' | 'fill_in_blank' | 'essay'
  difficulty?: number
  options?: QuestionOption[]
}

// 题库查询参数
export interface QuestionQueryParams {
  page?: number
  limit?: number
  course_id?: number
  question_type?: string
  difficulty?: number
  keyword?: string
}

// 题库分页响应
export interface QuestionListResponse {
  questions: Question[]
  pagination: {
    current_page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// 试卷题目关联类型
export interface ExamQuestion {
  exam_question_id?: number
  exam_id?: number
  question_id: number
  score: number
  question?: Question
}

// 试卷类型
export interface Exam {
  exam_id: number
  exam_name: string
  course_id: number
  creator_id: number
  start_time: string
  end_time: string
  duration: number
  total_score: number
  create_time: string
  course?: {
    course_name: string
  }
  creator?: {
    full_name: string
  }
  examQuestions?: ExamQuestion[]
  _count?: {
    examQuestions: number
  }
}

// 创建试卷请求类型
export interface CreateExamRequest {
  exam_name: string
  course_id: number
  start_time: string
  end_time: string
  duration: number
  total_score?: number
  questions: {
    question_id: number
    score: number
  }[]
}

// 更新试卷请求类型
export interface UpdateExamRequest {
  exam_name?: string
  course_id?: number
  start_time?: string
  end_time?: string
  duration?: number
  total_score?: number
  questions?: {
    question_id: number
    score: number
  }[]
}

// 试卷查询参数
export interface ExamQueryParams {
  page?: number
  limit?: number
  course_id?: number
  keyword?: string
}

// 试卷分页响应
export interface ExamListResponse {
  exams: Exam[]
  pagination: {
    current_page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// 自动组卷请求类型
export interface AutoGenerateExamRequest {
  course_id: number
  question_types?: string[]
  difficulties?: number[]
  question_count?: number
  total_score?: number
}

// 自动组卷响应类型
export interface AutoGenerateExamResponse {
  questions: (Question & { score: number })[]
  total_questions: number
  total_score: number
}

// 学生成绩类型
export interface StudentScore {
  score_id: number
  exam_id: number
  student_id: number
  total_score?: number
  submission_time?: string
  status: 'pending' | 'submitted' | 'marked'
  student?: {
    user_id: number
    username: string
    full_name: string
    email: string
  }
}

// 学生列表响应类型
export interface StudentListResponse {
  students: {
    user_id: number
    username: string
    full_name: string
    email: string
  }[]
  pagination: {
    current_page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// 考试统计类型
export interface ExamStatistics {
  total_students: number
  pending_count: number
  submitted_count: number
  marked_count: number
  average_score: number
  completion_rate: number
}

// 发布考试请求类型
export interface PublishExamRequest {
  student_ids: number[]
}

// 取消发布考试请求类型
export interface UnpublishExamRequest {
  student_ids?: number[]
}

// 学生答案类型
export interface StudentAnswer {
  answer_id: number
  score_id: number
  question_id: number
  student_answer?: string
  score: number
  is_marked: boolean
  comment?: string
  question?: Question
}

// 阅卷统计类型
export interface GradingStatistics {
  total_submitted: number
  total_marked: number
  pending_grading: number
  completion_rate: number
}

// 批阅题目请求类型
export interface ScoreQuestionRequest {
  answer_id: number
  score: number
  comment?: string
}

// 待批阅答卷列表响应类型
export interface PendingGradingResponse {
  student_scores: (StudentScore & {
    exam: {
      exam_name: string
      course: {
        course_name: string
      }
    }
    student: {
      username: string
      full_name: string
    }
    studentAnswers: StudentAnswer[]
  })[]
  pagination: {
    current_page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// 答卷详情类型
export interface AnswerSheetDetail extends StudentScore {
  exam: Exam & {
    course: {
      course_name: string
    }
    examQuestions: (ExamQuestion & {
      question: Question & {
        options: QuestionOption[]
      }
    })[]
  }
  student: {
    user_id: number
    username: string
    full_name: string
    email: string
  }
  studentAnswers: (StudentAnswer & {
    question: Question & {
      options: QuestionOption[]
    }
  })[]
}

// 考试统计分析类型
export interface ExamAnalytics {
  exam_info: {
    exam_name: string
    course_name: string
    total_score: number
  }
  basic_stats: {
    total_students: number
    average_score: number
    highest_score: number
    lowest_score: number
    pass_rate: number
    excellent_rate: number
  }
  score_distribution: {
    range: string
    count: number
    percentage: number
  }[]
  student_rankings: {
    rank: number
    student_name: string
    username: string
    score: number
    percentage: number
  }[]
}

// 题目分析类型
export interface QuestionAnalysis {
  question_id: number
  question_text: string
  question_type: string
  total_score: number
  total_answers: number
  correct_count: number
  correct_rate: number
  average_score: number
  option_analysis?: {
    option_text: string
    selected_count: number
    selected_rate: number
    is_correct: boolean
  }[]
}

// 教师概览统计类型
export interface TeacherOverview {
  overview: {
    total_exams: number
    total_questions: number
    total_participants: number
    average_score_percentage: number
  }
  recent_exams: {
    exam_id: number
    exam_name: string
    course_name: string
    start_time: string
    participant_count: number
    create_time: string
  }[]
}

// 课程对比分析类型
export interface CourseComparison {
  exam_id: number
  exam_name: string
  start_time: string
  total_students: number
  average_score: number
  pass_rate: number
  highest_score: number
  lowest_score: number
}[]
