<template>
  <div class="analytics-management">
    <div class="page-header">
      <h2>成绩分析</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 教师概览 -->
    <div v-if="teacherOverview" class="overview-section">
      <el-card>
        <template #header>
          <span>教学概览</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ teacherOverview.overview.total_exams }}</div>
              <div class="stat-label">总考试数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ teacherOverview.overview.total_questions }}</div>
              <div class="stat-label">总题目数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ teacherOverview.overview.total_participants }}</div>
              <div class="stat-label">参与人次</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ teacherOverview.overview.average_score_percentage }}%</div>
              <div class="stat-label">平均得分率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 考试选择 -->
    <div class="exam-selection">
      <el-card>
        <template #header>
          <span>选择考试进行详细分析</span>
        </template>
        <el-form :model="queryParams" inline>
          <el-form-item label="考试">
            <el-select
              v-model="queryParams.exam_id"
              placeholder="选择考试"
              style="width: 300px"
              @change="loadExamAnalysis"
            >
              <el-option
                v-for="exam in exams"
                :key="exam.exam_id"
                :label="`${exam.exam_name} (${exam.course?.course_name})`"
                :value="exam.exam_id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 考试统计分析 -->
    <div v-if="examAnalytics" class="exam-analytics">
      <!-- 基础统计 -->
      <el-card class="stats-card">
        <template #header>
          <span>{{ examAnalytics.exam_info.exam_name }} - 基础统计</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ examAnalytics.basic_stats.total_students }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ examAnalytics.basic_stats.average_score }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ examAnalytics.basic_stats.highest_score }}</div>
              <div class="stat-label">最高分</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ examAnalytics.basic_stats.lowest_score }}</div>
              <div class="stat-label">最低分</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ examAnalytics.basic_stats.pass_rate }}%</div>
              <div class="stat-label">及格率</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ examAnalytics.basic_stats.excellent_rate }}%</div>
              <div class="stat-label">优秀率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 分数分布图表 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>分数分布</span>
            </template>
            <div ref="scoreDistributionChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>学生排名 (前20名)</span>
            </template>
            <div class="rankings-list">
              <div
                v-for="student in examAnalytics.student_rankings"
                :key="student.rank"
                class="ranking-item"
              >
                <div class="rank">{{ student.rank }}</div>
                <div class="student-info">
                  <div class="name">{{ student.student_name }}</div>
                  <div class="username">{{ student.username }}</div>
                </div>
                <div class="score">
                  {{ student.score }} ({{ student.percentage }}%)
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 题目分析 -->
      <el-card v-if="questionAnalysis.length > 0" class="question-analysis-card">
        <template #header>
          <span>题目分析</span>
        </template>
        <el-table :data="questionAnalysis" style="width: 100%">
          <el-table-column type="index" label="题号" width="60" />
          <el-table-column prop="question_text" label="题目" width="300">
            <template #default="{ row }">
              <div class="question-text" v-html="row.question_text.substring(0, 100) + '...'"></div>
            </template>
          </el-table-column>
          <el-table-column prop="question_type" label="题型" width="100">
            <template #default="{ row }">
              {{ getQuestionTypeText(row.question_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="total_answers" label="作答人数" width="100" />
          <el-table-column prop="correct_rate" label="正确率" width="100">
            <template #default="{ row }">
              <el-tag :type="getCorrectRateColor(row.correct_rate)">
                {{ row.correct_rate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="average_score" label="平均得分" width="100">
            <template #default="{ row }">
              {{ row.average_score.toFixed(1) }} / {{ row.total_score }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="viewQuestionDetail(row)">
                详细分析
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 题目详细分析对话框 -->
    <QuestionAnalysisDialog
      v-model:visible="showQuestionDialog"
      :question="selectedQuestion"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { analyticsApi, examApi } from '@/api'
import type { 
  ExamAnalytics,
  QuestionAnalysis,
  TeacherOverview,
  Exam
} from '@/types/api'
import QuestionAnalysisDialog from '@/components/QuestionAnalysisDialog.vue'

// 响应式数据
const loading = ref(false)
const teacherOverview = ref<TeacherOverview | null>(null)
const exams = ref<Exam[]>([])
const examAnalytics = ref<ExamAnalytics | null>(null)
const questionAnalysis = ref<QuestionAnalysis[]>([])
const showQuestionDialog = ref(false)
const selectedQuestion = ref<QuestionAnalysis | null>(null)

// 图表引用
const scoreDistributionChart = ref<HTMLElement>()

// 查询参数
const queryParams = reactive({
  exam_id: undefined as number | undefined
})

// 页面加载
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  await Promise.all([
    loadTeacherOverview(),
    loadExams()
  ])
}

// 加载教师概览
const loadTeacherOverview = async () => {
  try {
    const response = await analyticsApi.getTeacherOverview()
    teacherOverview.value = response.data
  } catch (error) {
    console.error('加载教师概览失败:', error)
  }
}

// 加载考试列表
const loadExams = async () => {
  try {
    const response = await examApi.getExams({ page: 1, limit: 100 })
    exams.value = response.data.exams
  } catch (error) {
    console.error('加载考试列表失败:', error)
  }
}

// 加载考试分析
const loadExamAnalysis = async () => {
  if (!queryParams.exam_id) return

  try {
    loading.value = true
    const [analyticsResponse, questionResponse] = await Promise.all([
      analyticsApi.getExamStatistics(queryParams.exam_id),
      analyticsApi.getQuestionAnalysis(queryParams.exam_id)
    ])

    examAnalytics.value = analyticsResponse.data
    questionAnalysis.value = questionResponse.data

    // 渲染图表
    nextTick(() => {
      renderScoreDistributionChart()
    })
  } catch (error) {
    console.error('加载考试分析失败:', error)
    ElMessage.error('加载考试分析失败')
  } finally {
    loading.value = false
  }
}

// 渲染分数分布图表
const renderScoreDistributionChart = () => {
  if (!examAnalytics.value || !scoreDistributionChart.value) return

  // 这里可以使用 ECharts 或其他图表库
  // 为了简化，这里只是显示数据
  const chartData = examAnalytics.value.score_distribution
  console.log('分数分布数据:', chartData)
}

// 查看题目详细分析
const viewQuestionDetail = (question: QuestionAnalysis) => {
  selectedQuestion.value = question
  showQuestionDialog.value = true
}

// 刷新数据
const refreshData = () => {
  loadData()
  if (queryParams.exam_id) {
    loadExamAnalysis()
  }
}

// 工具函数
const getQuestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getCorrectRateColor = (rate: number) => {
  if (rate >= 80) return 'success'
  if (rate >= 60) return 'warning'
  return 'danger'
}
</script>

<style scoped>
.analytics-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.overview-section {
  margin-bottom: 20px;
}

.exam-selection {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stats-card {
  margin-bottom: 20px;
}

.rankings-list {
  max-height: 300px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.rank {
  width: 40px;
  height: 40px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.student-info {
  flex: 1;
}

.name {
  font-weight: 500;
  color: #303133;
}

.username {
  font-size: 12px;
  color: #909399;
}

.score {
  font-weight: bold;
  color: #67c23a;
}

.question-analysis-card {
  margin-top: 20px;
}

.question-text {
  line-height: 1.4;
  color: #606266;
}
</style>
