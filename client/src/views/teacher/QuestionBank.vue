<template>
  <div class="question-bank">
    <!-- 页面标题和操作按钮 -->
    <div class="header">
      <h1>题库管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建题目
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters">
      <el-form :model="queryParams" inline>
        <el-form-item label="课程">
          <el-select v-model="queryParams.course_id" placeholder="选择课程" clearable style="width: 200px">
            <el-option
              v-for="course in courses"
              :key="course.course_id"
              :label="course.course_name"
              :value="course.course_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题型">
          <el-select v-model="queryParams.question_type" placeholder="选择题型" clearable style="width: 150px">
            <el-option label="单选题" value="single_choice" />
            <el-option label="多选题" value="multiple_choice" />
            <el-option label="判断题" value="true_false" />
            <el-option label="填空题" value="fill_in_blank" />
            <el-option label="简答题" value="essay" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select v-model="queryParams.difficulty" placeholder="选择难度" clearable style="width: 120px">
            <el-option label="简单" :value="1" />
            <el-option label="中等" :value="2" />
            <el-option label="困难" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索题目内容"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadQuestions">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 题目列表 -->
    <div class="question-list">
      <el-table
        v-loading="loading"
        :data="questions"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="question_id" label="ID" width="80" />
        <el-table-column prop="question_text" label="题目内容" min-width="300">
          <template #default="{ row }">
            <div class="question-content">
              {{ row.question_text.length > 100 ? row.question_text.substring(0, 100) + '...' : row.question_text }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="question_type" label="题型" width="100">
          <template #default="{ row }">
            <el-tag :type="getQuestionTypeColor(row.question_type)">
              {{ getQuestionTypeName(row.question_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="course.course_name" label="课程" width="150" />
        <el-table-column prop="difficulty" label="难度" width="100">
          <template #default="{ row }">
            <el-tag :type="getDifficultyColor(row.difficulty)">
              {{ getDifficultyName(row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewQuestion(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editQuestion(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteQuestion(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedQuestions.length > 0" class="batch-actions">
        <el-button type="danger" @click="batchDelete">
          批量删除 ({{ selectedQuestions.length }})
        </el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadQuestions"
          @current-change="loadQuestions"
        />
      </div>
    </div>

    <!-- 创建/编辑题目对话框 -->
    <QuestionDialog
      v-model:visible="showCreateDialog"
      :courses="courses"
      :question="editingQuestion"
      @success="handleDialogSuccess"
    />

    <!-- 题目详情对话框 -->
    <QuestionDetailDialog
      v-model:visible="showDetailDialog"
      :question="viewingQuestion"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { questionApi, courseApi } from '@/api'
import type { Question, Course, QuestionQueryParams } from '@/types/api'
import QuestionDialog from '@/components/QuestionDialog.vue'
import QuestionDetailDialog from '@/components/QuestionDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const questions = ref<Question[]>([])
const courses = ref<Course[]>([])
const selectedQuestions = ref<Question[]>([])
const total = ref(0)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingQuestion = ref<Question | null>(null)
const viewingQuestion = ref<Question | null>(null)

// 查询参数
const queryParams = reactive<QuestionQueryParams>({
  page: 1,
  limit: 20,
  course_id: undefined,
  question_type: undefined,
  difficulty: undefined,
  keyword: ''
})

// 加载题目列表
const loadQuestions = async () => {
  try {
    loading.value = true
    const response = await questionApi.getQuestions(queryParams)
    questions.value = response.data.questions
    total.value = response.data.pagination.total
  } catch (error) {
    console.error('加载题目列表失败:', error)
    ElMessage.error('加载题目列表失败')
  } finally {
    loading.value = false
  }
}

// 加载课程列表
const loadCourses = async () => {
  try {
    const response = await courseApi.getCourses()
    courses.value = response.data
  } catch (error) {
    console.error('加载课程列表失败:', error)
    ElMessage.error('加载课程列表失败')
  }
}

// 重置筛选条件
const resetFilters = () => {
  queryParams.course_id = undefined
  queryParams.question_type = undefined
  queryParams.difficulty = undefined
  queryParams.keyword = ''
  queryParams.page = 1
  loadQuestions()
}

// 处理选择变化
const handleSelectionChange = (selection: Question[]) => {
  selectedQuestions.value = selection
}

// 查看题目详情
const viewQuestion = (question: Question) => {
  viewingQuestion.value = question
  showDetailDialog.value = true
}

// 编辑题目
const editQuestion = (question: Question) => {
  editingQuestion.value = question
  showCreateDialog.value = true
}

// 对话框成功回调
const handleDialogSuccess = () => {
  editingQuestion.value = null
  loadQuestions()
}

// 从详情对话框编辑
const handleEditFromDetail = (question: Question) => {
  editingQuestion.value = question
  showCreateDialog.value = true
}

// 删除题目
const deleteQuestion = async (question: Question) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除题目"${question.question_text.substring(0, 50)}..."吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await questionApi.deleteQuestion(question.question_id)
    ElMessage.success('删除成功')
    loadQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除题目失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedQuestions.value.length} 个题目吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedQuestions.value.map(q => q.question_id)
    await questionApi.batchDeleteQuestions(ids)
    ElMessage.success('批量删除成功')
    loadQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 工具函数
const getQuestionTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_in_blank: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || type
}

const getQuestionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    single_choice: 'primary',
    multiple_choice: 'success',
    true_false: 'warning',
    fill_in_blank: 'info',
    essay: 'danger'
  }
  return colorMap[type] || ''
}

const getDifficultyName = (difficulty: number) => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

const getDifficultyColor = (difficulty: number) => {
  const colorMap: Record<number, string> = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return colorMap[difficulty] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadCourses()
  loadQuestions()
})
</script>

<style scoped>
.question-bank {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  color: #303133;
}

.filters {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.question-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.question-content {
  line-height: 1.5;
  word-break: break-word;
}

.batch-actions {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
