<template>
  <div class="exam-management">
    <!-- 页面标题和操作按钮 -->
    <div class="header">
      <h1>试卷管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建试卷
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters">
      <el-form :model="queryParams" inline>
        <el-form-item label="课程">
          <el-select v-model="queryParams.course_id" placeholder="选择课程" clearable style="width: 200px">
            <el-option
              v-for="course in courses"
              :key="course.course_id"
              :label="course.course_name"
              :value="course.course_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索试卷名称"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadExams">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 试卷列表 -->
    <div class="exam-list">
      <el-table
        v-loading="loading"
        :data="exams"
        style="width: 100%"
      >
        <el-table-column prop="exam_id" label="ID" width="80" />
        <el-table-column prop="exam_name" label="试卷名称" min-width="200" />
        <el-table-column prop="course.course_name" label="课程" width="150" />
        <el-table-column label="题目数量" width="100">
          <template #default="{ row }">
            {{ row._count?.examQuestions || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="total_score" label="总分" width="80" />
        <el-table-column prop="duration" label="时长(分钟)" width="100" />
        <el-table-column label="考试时间" width="300">
          <template #default="{ row }">
            <div>
              <div>开始：{{ formatDateTime(row.start_time) }}</div>
              <div>结束：{{ formatDateTime(row.end_time) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewExam(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editExam(row)">编辑</el-button>
            <el-button size="small" type="success" @click="previewExam(row)">预览</el-button>
            <el-button size="small" type="warning" @click="publishExam(row)">发布</el-button>
            <el-button size="small" type="danger" @click="deleteExam(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadExams"
          @current-change="loadExams"
        />
      </div>
    </div>

    <!-- 创建/编辑试卷对话框 -->
    <ExamDialog
      v-model:visible="showCreateDialog"
      :courses="courses"
      :exam="editingExam"
      @success="handleDialogSuccess"
    />

    <!-- 试卷预览对话框 -->
    <ExamPreviewDialog
      v-model:visible="showPreviewDialog"
      :exam="previewingExam"
    />

    <!-- 考试发布对话框 -->
    <ExamPublishDialog
      v-model:visible="showPublishDialog"
      :exam="publishingExam"
      @success="handlePublishSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { examApi, courseApi } from '@/api'
import type { Exam, Course, ExamQueryParams } from '@/types/api'
import ExamDialog from '@/components/ExamDialog.vue'
import ExamPreviewDialog from '@/components/ExamPreviewDialog.vue'
import ExamPublishDialog from '@/components/ExamPublishDialog.vue'

// 响应式数据
const loading = ref(false)
const exams = ref<Exam[]>([])
const courses = ref<Course[]>([])
const total = ref(0)
const showCreateDialog = ref(false)
const showPreviewDialog = ref(false)
const showPublishDialog = ref(false)
const editingExam = ref<Exam | null>(null)
const previewingExam = ref<Exam | null>(null)
const publishingExam = ref<Exam | null>(null)

// 查询参数
const queryParams = reactive<ExamQueryParams>({
  page: 1,
  limit: 20,
  course_id: undefined,
  keyword: ''
})

// 加载试卷列表
const loadExams = async () => {
  try {
    loading.value = true
    const response = await examApi.getExams(queryParams)
    exams.value = response.data.exams
    total.value = response.data.pagination.total
  } catch (error) {
    console.error('加载试卷列表失败:', error)
    ElMessage.error('加载试卷列表失败')
  } finally {
    loading.value = false
  }
}

// 加载课程列表
const loadCourses = async () => {
  try {
    const response = await courseApi.getCourses()
    courses.value = response.data
  } catch (error) {
    console.error('加载课程列表失败:', error)
    ElMessage.error('加载课程列表失败')
  }
}

// 重置筛选条件
const resetFilters = () => {
  queryParams.course_id = undefined
  queryParams.keyword = ''
  queryParams.page = 1
  loadExams()
}

// 查看试卷详情
const viewExam = (exam: Exam) => {
  // TODO: 实现查看试卷详情
  console.log('查看试卷:', exam)
}

// 编辑试卷
const editExam = (exam: Exam) => {
  editingExam.value = exam
  showCreateDialog.value = true
}

// 预览试卷
const previewExam = async (exam: Exam) => {
  try {
    const response = await examApi.getExamById(exam.exam_id)
    previewingExam.value = response.data
    showPreviewDialog.value = true
  } catch (error) {
    console.error('获取试卷详情失败:', error)
    ElMessage.error('获取试卷详情失败')
  }
}

// 删除试卷
const deleteExam = async (exam: Exam) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除试卷"${exam.exam_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await examApi.deleteExam(exam.exam_id)
    ElMessage.success('删除成功')
    loadExams()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除试卷失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 发布考试
const publishExam = (exam: Exam) => {
  publishingExam.value = exam
  showPublishDialog.value = true
}

// 对话框成功回调
const handleDialogSuccess = () => {
  editingExam.value = null
  loadExams()
}

// 发布成功回调
const handlePublishSuccess = () => {
  publishingExam.value = null
  loadExams()
}

// 工具函数
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadCourses()
  loadExams()
})
</script>

<style scoped>
.exam-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  color: #303133;
}

.filters {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.exam-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
