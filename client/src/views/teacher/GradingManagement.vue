<template>
  <div class="grading-management">
    <div class="page-header">
      <h2>阅卷管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div v-if="statistics" class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.total_submitted }}</div>
              <div class="stat-label">总提交数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.total_marked }}</div>
              <div class="stat-label">已批阅数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.pending_grading }}</div>
              <div class="stat-label">待批阅题数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.completion_rate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" inline>
        <el-form-item label="考试">
          <el-select
            v-model="queryParams.exam_id"
            placeholder="选择考试"
            clearable
            style="width: 200px"
            @change="loadPendingGrading"
          >
            <el-option
              v-for="exam in exams"
              :key="exam.exam_id"
              :label="exam.exam_name"
              :value="exam.exam_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题型">
          <el-select
            v-model="queryParams.question_type"
            placeholder="选择题型"
            clearable
            style="width: 150px"
            @change="loadPendingGrading"
          >
            <el-option label="填空题" value="fill_in_blank" />
            <el-option label="简答题" value="essay" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadPendingGrading">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 待批阅答卷列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>待批阅答卷</span>
          <div class="header-actions">
            <el-button
              v-if="queryParams.exam_id"
              type="success"
              size="small"
              @click="autoGradeObjective"
            >
              自动批阅客观题
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="pendingList"
        style="width: 100%"
      >
        <el-table-column prop="exam.exam_name" label="考试名称" width="200" />
        <el-table-column prop="exam.course.course_name" label="课程" width="150" />
        <el-table-column prop="student.full_name" label="学生姓名" width="120" />
        <el-table-column prop="student.username" label="用户名" width="120" />
        <el-table-column prop="submission_time" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.submission_time) }}
          </template>
        </el-table-column>
        <el-table-column label="待批阅题数" width="120">
          <template #default="{ row }">
            {{ getUnmarkedCount(row.studentAnswers) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="startGrading(row)">
              开始批阅
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadPendingGrading"
          @current-change="loadPendingGrading"
        />
      </div>
    </el-card>

    <!-- 阅卷对话框 -->
    <GradingDialog
      v-model:visible="showGradingDialog"
      :score-id="gradingScoreId"
      @success="handleGradingSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { gradingApi, examApi } from '@/api'
import type { 
  PendingGradingResponse,
  GradingStatistics,
  Exam,
  StudentAnswer
} from '@/types/api'
import GradingDialog from '@/components/GradingDialog.vue'

// 响应式数据
const loading = ref(false)
const pendingList = ref<PendingGradingResponse['student_scores']>([])
const statistics = ref<GradingStatistics | null>(null)
const exams = ref<Exam[]>([])
const total = ref(0)
const showGradingDialog = ref(false)
const gradingScoreId = ref<number | null>(null)

// 查询参数
const queryParams = reactive({
  exam_id: undefined as number | undefined,
  question_type: undefined as string | undefined,
  page: 1,
  limit: 20
})

// 页面加载
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  await Promise.all([
    loadExams(),
    loadPendingGrading(),
    loadStatistics()
  ])
}

// 加载考试列表
const loadExams = async () => {
  try {
    const response = await examApi.getExams({ page: 1, limit: 100 })
    exams.value = response.data.exams
  } catch (error) {
    console.error('加载考试列表失败:', error)
  }
}

// 加载待批阅答卷
const loadPendingGrading = async () => {
  try {
    loading.value = true
    const response = await gradingApi.getPendingGrading(queryParams)
    pendingList.value = response.data.student_scores
    total.value = response.data.pagination.total
  } catch (error) {
    console.error('加载待批阅答卷失败:', error)
    ElMessage.error('加载待批阅答卷失败')
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStatistics = async () => {
  try {
    const response = await gradingApi.getGradingStatistics({
      exam_id: queryParams.exam_id
    })
    statistics.value = response.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 重置查询
const resetQuery = () => {
  queryParams.exam_id = undefined
  queryParams.question_type = undefined
  queryParams.page = 1
  loadPendingGrading()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 开始批阅
const startGrading = (studentScore: any) => {
  gradingScoreId.value = studentScore.score_id
  showGradingDialog.value = true
}

// 批阅成功回调
const handleGradingSuccess = () => {
  gradingScoreId.value = null
  loadData()
}

// 自动批阅客观题
const autoGradeObjective = async () => {
  if (!queryParams.exam_id) return

  try {
    await ElMessageBox.confirm(
      '确定要自动批阅该考试的所有客观题吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await gradingApi.autoGradeObjective(queryParams.exam_id)
    ElMessage.success(`成功自动批阅 ${response.data.graded_count} 道客观题`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('自动批阅失败:', error)
      ElMessage.error('自动批阅失败')
    }
  }
}

// 工具函数
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getUnmarkedCount = (answers: StudentAnswer[]) => {
  return answers.filter(answer => 
    !answer.is_marked && 
    answer.question?.question_type && 
    ['fill_in_blank', 'essay'].includes(answer.question.question_type)
  ).length
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待考试',
    submitted: '已提交',
    marked: '已批阅'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'info',
    submitted: 'warning',
    marked: 'success'
  }
  return colorMap[status] || ''
}
</script>

<style scoped>
.grading-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
