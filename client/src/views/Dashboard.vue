<template>
  <div class="dashboard-container">
    <div class="welcome-section">
      <h1>欢迎使用在线考试系统</h1>
      <p>您好，{{ userStore.userName }}！</p>
    </div>
    
    <div class="role-navigation">
      <el-card v-if="userStore.isTeacher" class="nav-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><User /></el-icon>
            <span>教师端</span>
          </div>
        </template>
        <p>管理题库、创建试卷、发布考试、阅卷评分</p>
        <el-button type="primary" @click="$router.push('/teacher')">
          进入教师端
        </el-button>
      </el-card>
      
      <el-card v-if="userStore.isStudent" class="nav-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Reading /></el-icon>
            <span>学生端</span>
          </div>
        </template>
        <p>查看考试、参加考试、查询成绩</p>
        <el-button type="primary" @click="$router.push('/student')">
          进入学生端
        </el-button>
      </el-card>
      
      <el-card class="nav-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>个人设置</span>
          </div>
        </template>
        <p>修改个人信息、更改密码</p>
        <el-button @click="handleSettings">
          个人设置
        </el-button>
      </el-card>
    </div>
    
    <div class="user-info">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>用户信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">
            {{ userStore.userInfo?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="姓名">
            {{ userStore.userInfo?.full_name || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="getRoleTagType(userStore.userRole)">
              {{ getRoleText(userStore.userRole) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ userStore.userInfo?.email || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item v-if="userStore.isStudent" label="学号">
            {{ userStore.userInfo?.student_id || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(userStore.userInfo?.create_time) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
    
    <div class="logout-section">
      <el-button type="danger" @click="handleLogout">
        退出登录
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Reading, Setting, InfoFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'teacher':
      return 'warning'
    case 'student':
      return 'success'
    default:
      return 'info'
  }
}

// 获取角色文本
const getRoleText = (role: string) => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'teacher':
      return '教师'
    case 'student':
      return '学生'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 处理个人设置
const handleSettings = () => {
  ElMessage.info('个人设置功能开发中...')
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.welcome-section p {
  color: #666;
  font-size: 16px;
}

.role-navigation {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.nav-card {
  text-align: center;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 600;
}

.nav-card p {
  color: #666;
  margin: 15px 0;
  line-height: 1.6;
}

.user-info {
  margin-bottom: 40px;
}

.logout-section {
  text-align: center;
}
</style>
