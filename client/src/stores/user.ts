import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api/auth'
import type { User, LoginRequest } from '@/types/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<User | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const userRole = computed(() => userInfo.value?.role || '')
  const userName = computed(() => userInfo.value?.full_name || userInfo.value?.username || '')

  // 初始化用户信息（从localStorage恢复）
  const initUserInfo = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        userInfo.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearUserInfo()
      }
    }
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      isLoading.value = true
      const response = await authApi.login(loginData)
      
      if (response.data) {
        const { token: newToken, user } = response.data
        
        // 保存到状态
        token.value = newToken
        userInfo.value = user
        
        // 保存到localStorage
        localStorage.setItem('token', newToken)
        localStorage.setItem('user', JSON.stringify(user))
        
        ElMessage.success('登录成功')
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    clearUserInfo()
    ElMessage.success('已退出登录')
  }

  // 清除用户信息
  const clearUserInfo = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 获取当前用户信息（用于验证token有效性）
  const fetchCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser()
      if (response.data) {
        userInfo.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
        return true
      }
      return false
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearUserInfo()
      return false
    }
  }

  // 检查权限
  const hasRole = (role: string | string[]) => {
    if (!userInfo.value) return false
    
    if (Array.isArray(role)) {
      return role.includes(userInfo.value.role)
    }
    return userInfo.value.role === role
  }

  // 检查是否为教师
  const isTeacher = computed(() => hasRole('teacher'))
  
  // 检查是否为学生
  const isStudent = computed(() => hasRole('student'))
  
  // 检查是否为管理员
  const isAdmin = computed(() => hasRole('admin'))

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userRole,
    userName,
    isTeacher,
    isStudent,
    isAdmin,
    
    // 方法
    initUserInfo,
    login,
    logout,
    clearUserInfo,
    fetchCurrentUser,
    hasRole
  }
})
