import { http } from '@/utils/request'
import type { 
  ApiResponse, 
  Question, 
  CreateQuestionRequest, 
  UpdateQuestionRequest,
  QuestionQueryParams,
  QuestionListResponse
} from '@/types/api'

/**
 * 题库管理API
 */
export const questionApi = {
  /**
   * 获取题库列表（支持分页和筛选）
   */
  getQuestions(params: QuestionQueryParams = {}): Promise<ApiResponse<QuestionListResponse>> {
    return http.get('/api/questions', { params })
  },

  /**
   * 获取单个题目详情
   */
  getQuestionById(id: number): Promise<ApiResponse<Question>> {
    return http.get(`/api/questions/${id}`)
  },

  /**
   * 创建新题目
   */
  createQuestion(data: CreateQuestionRequest): Promise<ApiResponse<{ question_id: number }>> {
    return http.post('/api/questions', data)
  },

  /**
   * 更新题目
   */
  updateQuestion(id: number, data: UpdateQuestionRequest): Promise<ApiResponse<void>> {
    return http.put(`/api/questions/${id}`, data)
  },

  /**
   * 删除题目
   */
  deleteQuestion(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/api/questions/${id}`)
  },

  /**
   * 批量删除题目
   */
  batchDeleteQuestions(ids: number[]): Promise<ApiResponse<void>> {
    return http.post('/api/questions/batch-delete', { ids })
  }
}
