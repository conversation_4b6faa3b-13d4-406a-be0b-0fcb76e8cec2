import { http } from '@/utils/request'
import type { 
  ApiResponse,
  PendingGradingResponse,
  AnswerSheetDetail,
  ScoreQuestionRequest,
  GradingStatistics
} from '@/types/api'

export const gradingApi = {
  /**
   * 获取待批阅的答卷列表
   */
  getPendingGrading(params: {
    exam_id?: number
    question_type?: string
    page?: number
    limit?: number
  } = {}): Promise<ApiResponse<PendingGradingResponse>> {
    return http.get('/api/grading/pending', { params })
  },

  /**
   * 获取单个学生的答卷详情
   */
  getAnswerSheet(scoreId: number): Promise<ApiResponse<AnswerSheetDetail>> {
    return http.get(`/api/grading/answer/${scoreId}`)
  },

  /**
   * 批阅单题
   */
  scoreQuestion(data: ScoreQuestionRequest): Promise<ApiResponse<void>> {
    return http.put('/api/grading/score-question', data)
  },

  /**
   * 完成整份答卷批阅
   */
  completeGrading(scoreId: number): Promise<ApiResponse<{ total_score: number }>> {
    return http.put(`/api/grading/complete/${scoreId}`)
  },

  /**
   * 获取阅卷统计信息
   */
  getGradingStatistics(params: { exam_id?: number } = {}): Promise<ApiResponse<GradingStatistics>> {
    return http.get('/api/grading/statistics', { params })
  },

  /**
   * 批量自动批阅客观题
   */
  autoGradeObjective(examId: number): Promise<ApiResponse<{ graded_count: number }>> {
    return http.post(`/api/grading/auto-grade/${examId}`)
  }
}
