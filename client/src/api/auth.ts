import { http } from '@/utils/request'
import type { LoginRequest, LoginResponse, User } from '@/types/api'

// 认证相关API
export const authApi = {
  // 用户登录
  login(data: LoginRequest) {
    return http.post<LoginResponse>('/api/auth/login', data)
  },
  
  // 获取当前用户信息
  getCurrentUser() {
    return http.get<User>('/api/auth/me')
  },
  
  // 用户注册（测试用）
  register(data: {
    username: string
    password: string
    full_name: string
    role: 'student' | 'teacher'
    email?: string
    student_id?: string
  }) {
    return http.post('/api/auth/register', data)
  }
}
