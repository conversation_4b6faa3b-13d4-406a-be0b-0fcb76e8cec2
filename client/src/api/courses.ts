import { http } from '@/utils/request'
import type { ApiResponse, Course } from '@/types/api'

/**
 * 课程管理API
 */
export const courseApi = {
  /**
   * 获取课程列表
   */
  getCourses(): Promise<ApiResponse<Course[]>> {
    return http.get('/api/courses')
  },

  /**
   * 创建课程（仅管理员）
   */
  createCourse(data: { course_name: string; description?: string }): Promise<ApiResponse<Course>> {
    return http.post('/api/courses', data)
  }
}
