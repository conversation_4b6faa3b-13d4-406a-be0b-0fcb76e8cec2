import { http } from '@/utils/request'
import type { 
  ApiResponse,
  ExamAnalytics,
  QuestionAnalysis,
  TeacherOverview,
  CourseComparison
} from '@/types/api'

export const analyticsApi = {
  /**
   * 获取考试成绩统计分析
   */
  getExamStatistics(examId: number): Promise<ApiResponse<ExamAnalytics>> {
    return http.get(`/api/analytics/exam/${examId}/statistics`)
  },

  /**
   * 获取题目正确率分析
   */
  getQuestionAnalysis(examId: number): Promise<ApiResponse<QuestionAnalysis[]>> {
    return http.get(`/api/analytics/exam/${examId}/question-analysis`)
  },

  /**
   * 获取教师整体教学统计
   */
  getTeacherOverview(): Promise<ApiResponse<TeacherOverview>> {
    return http.get('/api/analytics/teacher/overview')
  },

  /**
   * 获取课程成绩对比分析
   */
  getCourseComparison(courseId: number): Promise<ApiResponse<CourseComparison>> {
    return http.get(`/api/analytics/course/${courseId}/comparison`)
  }
}
