import { http } from '@/utils/request'
import type {
  ApiResponse,
  Exam,
  CreateExamRequest,
  UpdateExamRequest,
  ExamQueryParams,
  ExamListResponse,
  AutoGenerateExamRequest,
  AutoGenerateExamResponse,
  Question,
  QuestionQueryParams,
  QuestionListResponse,
  PublishExamRequest,
  UnpublishExamRequest,
  StudentScore,
  StudentListResponse,
  ExamStatistics
} from '@/types/api'

/**
 * 试卷管理API
 */
export const examApi = {
  /**
   * 获取试卷列表（支持分页和筛选）
   */
  getExams(params: ExamQueryParams = {}): Promise<ApiResponse<ExamListResponse>> {
    return http.get('/api/exams', { params })
  },

  /**
   * 获取单个试卷详情
   */
  getExamById(id: number): Promise<ApiResponse<Exam>> {
    return http.get(`/api/exams/${id}`)
  },

  /**
   * 创建试卷
   */
  createExam(data: CreateExamRequest): Promise<ApiResponse<{ exam_id: number }>> {
    return http.post('/api/exams', data)
  },

  /**
   * 更新试卷
   */
  updateExam(id: number, data: UpdateExamRequest): Promise<ApiResponse<void>> {
    return http.put(`/api/exams/${id}`, data)
  },

  /**
   * 删除试卷
   */
  deleteExam(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/api/exams/${id}`)
  },

  /**
   * 自动组卷
   */
  autoGenerateExam(data: AutoGenerateExamRequest): Promise<ApiResponse<AutoGenerateExamResponse>> {
    return http.post('/api/exams/auto-generate', data)
  },

  /**
   * 获取可用于组卷的题目列表
   */
  getAvailableQuestions(params: QuestionQueryParams & { course_id: number }): Promise<ApiResponse<QuestionListResponse>> {
    return http.get('/api/exams/questions/available', { params })
  },

  /**
   * 发布考试
   */
  publishExam(id: number, data: PublishExamRequest): Promise<ApiResponse<void>> {
    return http.post(`/api/exams/${id}/publish`, data)
  },

  /**
   * 获取考试参与学生列表
   */
  getExamStudents(id: number): Promise<ApiResponse<StudentScore[]>> {
    return http.get(`/api/exams/${id}/students`)
  },

  /**
   * 取消发布考试
   */
  unpublishExam(id: number, data: UnpublishExamRequest = {}): Promise<ApiResponse<void>> {
    return http.delete(`/api/exams/${id}/unpublish`, { data })
  },

  /**
   * 获取可选择的学生列表
   */
  getAvailableStudents(params: { keyword?: string; page?: number; limit?: number } = {}): Promise<ApiResponse<StudentListResponse>> {
    return http.get('/api/exams/students/available', { params })
  },

  /**
   * 获取考试统计信息
   */
  getExamStatistics(id: number): Promise<ApiResponse<ExamStatistics>> {
    return http.get(`/api/exams/${id}/statistics`)
  }
}
