import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true
    }
  },
  // 教师路由
  {
    path: '/teacher',
    name: 'Teacher',
    component: () => import('@/views/teacher/Layout.vue'),
    meta: {
      title: '教师端',
      requiresAuth: true,
      roles: ['teacher']
    },
    children: [
      {
        path: '',
        redirect: '/teacher/dashboard'
      },
      {
        path: 'dashboard',
        name: 'TeacherDashboard',
        component: () => import('@/views/teacher/Dashboard.vue'),
        meta: {
          title: '教师仪表板',
          requiresAuth: true,
          roles: ['teacher']
        }
      },
      {
        path: 'questions',
        name: 'QuestionBank',
        component: () => import('@/views/teacher/QuestionBank.vue'),
        meta: {
          title: '题库管理',
          requiresAuth: true,
          roles: ['teacher']
        }
      },
      {
        path: 'exams',
        name: 'ExamManagement',
        component: () => import('@/views/teacher/ExamManagement.vue'),
        meta: {
          title: '考试管理',
          requiresAuth: true,
          roles: ['teacher']
        }
      },
      {
        path: 'grading',
        name: 'Grading',
        component: () => import('@/views/teacher/GradingManagement.vue'),
        meta: {
          title: '阅卷管理',
          requiresAuth: true,
          roles: ['teacher']
        }
      },
      {
        path: 'analytics',
        name: 'Analytics',
        component: () => import('@/views/teacher/AnalyticsManagement.vue'),
        meta: {
          title: '成绩分析',
          requiresAuth: true,
          roles: ['teacher']
        }
      }
    ]
  },
  // 学生路由
  {
    path: '/student',
    name: 'Student',
    component: () => import('@/views/student/Layout.vue'),
    meta: {
      title: '学生端',
      requiresAuth: true,
      roles: ['student']
    },
    children: [
      {
        path: '',
        redirect: '/student/dashboard'
      },
      {
        path: 'dashboard',
        name: 'StudentDashboard',
        component: () => import('@/views/student/Dashboard.vue'),
        meta: {
          title: '学生仪表板',
          requiresAuth: true,
          roles: ['student']
        }
      },
      {
        path: 'exams',
        name: 'StudentExams',
        component: () => import('@/views/student/ExamList.vue'),
        meta: {
          title: '考试列表',
          requiresAuth: true,
          roles: ['student']
        }
      },
      {
        path: 'exam/:id',
        name: 'TakeExam',
        component: () => import('@/views/student/TakeExam.vue'),
        meta: {
          title: '参加考试',
          requiresAuth: true,
          roles: ['student']
        }
      },
      {
        path: 'scores',
        name: 'StudentScores',
        component: () => import('@/views/student/ScoreHistory.vue'),
        meta: {
          title: '成绩查询',
          requiresAuth: true,
          roles: ['student']
        }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 在线考试系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果没有登录，跳转到登录页
    if (!userStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查角色权限
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      if (!userStore.hasRole(to.meta.roles)) {
        ElMessage.error('权限不足')
        next('/dashboard')
        return
      }
    }
  }

  // 如果已登录且访问登录页，重定向到仪表板
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/dashboard')
    return
  }

  next()
})

export default router
