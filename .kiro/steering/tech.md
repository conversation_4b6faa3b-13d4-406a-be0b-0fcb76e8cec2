# Technology Stack

## Backend (server/)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL with utf8mb4 charset
- **ORM**: Prisma Client + raw MySQL2 connection pool
- **Authentication**: JWT (JSON Web Tokens) + bcrypt for password hashing
- **Environment**: dotenv for configuration
- **File Upload**: Multer
- **CORS**: Enabled for cross-origin requests

## Frontend Applications

### Admin Panel (admin/)
- **Framework**: Vue 3 with Composition API
- **Language**: TypeScript
- **Build Tool**: Vite (using rolldown-vite)
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Linting**: ESLint + oxlint
- **Formatting**: Prettier

### Client Application (client/)
- **Framework**: Vue 3 with Composition API  
- **Language**: TypeScript
- **Build Tool**: Vite (using rolldown-vite)
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Linting**: ESLint + oxlint
- **Formatting**: Prettier

## Database Schema
- **Engine**: InnoDB with foreign key constraints
- **Charset**: utf8mb4_unicode_ci for full Unicode support
- **Key Tables**: users, courses, questions, question_options, exams, exam_questions, student_scores, student_answers

## Common Commands

### Server Development
```bash
cd server
npm run dev          # Start with nodemon (auto-reload)
npm start           # Production start
```

### Frontend Development
```bash
cd admin            # or cd client
npm run dev         # Start development server
npm run build       # Production build
npm run preview     # Preview production build
npm run lint        # Run linting
npm run format      # Format code
```

## Environment Configuration
- Server uses `.env` file for database credentials and JWT secrets
- Database connection supports environment variables: `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`