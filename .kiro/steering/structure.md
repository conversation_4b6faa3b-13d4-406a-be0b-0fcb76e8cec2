# Project Structure

## Root Directory Layout
```
├── admin/           # Admin panel Vue application
├── client/          # Student/Teacher Vue application  
├── server/          # Express.js backend API
├── sql.sql          # Database schema definition
└── 功能清单.txt      # Feature requirements (Chinese)
```

## Backend Structure (server/)
```
server/
├── config/
│   ├── db.js        # MySQL connection pool setup
│   └── prisma.js    # Prisma client configuration
├── node_modules/    # Dependencies
├── package.json     # Server dependencies and scripts
└── bun.lock        # Lock file
```

### Backend Conventions
- **Database Access**: Dual approach using both Prisma ORM and raw MySQL2 queries
- **Connection Management**: Connection pooling with 10 max connections
- **Error Handling**: Centralized error logging with Chinese messages
- **Environment**: Uses dotenv for configuration management
- **Module System**: CommonJS (require/module.exports)

## Frontend Structure (admin/ & client/)
```
admin/ (or client/)
├── src/
│   ├── App.vue      # Root component
│   ├── main.ts      # Application entry point
│   ├── router/      # Vue Router configuration
│   └── stores/      # Pinia state management
├── public/
│   └── favicon.ico  # Static assets
├── package.json     # Dependencies and scripts
├── vite.config.ts   # Vite build configuration
├── tsconfig.*.json  # TypeScript configurations
└── eslint.config.ts # ESLint configuration
```

### Frontend Conventions
- **Component Style**: Vue 3 Composition API with `<script setup>`
- **Language**: TypeScript throughout
- **State Management**: Pinia stores in `/src/stores/`
- **Routing**: Vue Router 4 in `/src/router/`
- **Build Tool**: Vite with rolldown-vite variant
- **Code Quality**: ESLint + oxlint + Prettier integration

## Database Schema Organization
- **Users Table**: Central authentication with role-based access (student/teacher/admin)
- **Course Management**: Courses linked to questions and exams
- **Question Bank**: Questions with options, supporting multiple question types
- **Exam System**: Exams linked to questions with scoring
- **Results Tracking**: Student scores and detailed answers with grading status

## Development Workflow
1. **Database First**: Schema defined in `sql.sql`
2. **Backend API**: Express routes with authentication middleware
3. **Frontend Integration**: Separate admin and client applications
4. **Role Separation**: Different frontends for different user types

## File Naming Conventions
- **Vue Components**: PascalCase (e.g., `App.vue`)
- **JavaScript/TypeScript**: camelCase for files and variables
- **Database**: snake_case for table and column names
- **Configuration**: kebab-case for config files