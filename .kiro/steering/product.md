# Product Overview

This is an **Online Exam System** (在线考试系统) designed for educational institutions. The system supports three user roles:

- **Students**: Take online exams, view results and history
- **Teachers**: Create question banks, compose exam papers, publish exams, grade subjective questions, and analyze results
- **Administrators**: Manage users, courses, and system announcements

## Core Features

- **Question Bank Management**: Support for multiple question types (single choice, multiple choice, true/false, fill-in-blank, essay)
- **Exam Paper Creation**: Manual and automatic paper generation with difficulty and topic filtering
- **Online Examination**: Real-time exam taking with countdown timer, answer saving, and submission
- **Automated Grading**: Automatic scoring for objective questions, manual grading for subjective questions
- **Result Analysis**: Statistical analysis with charts showing class averages, pass rates, and question accuracy
- **User Management**: Role-based access control with JWT authentication

## Development Priority

1. **Foundation**: Backend framework, database setup, authentication system
2. **Teacher Module**: Question bank, exam paper creation, grading, analytics
3. **Student Module**: Exam taking, result viewing
4. **Admin <PERSON>le**: User management, system administration